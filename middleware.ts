import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected routes and their required roles
const protectedRoutes = {
  '/admin': ['super_admin', 'admin'],
  '/admin/institution-config': ['super_admin'],
  '/admin/streams': ['super_admin', 'admin'],
  '/admin/sections': ['super_admin', 'admin'],
  '/admin/users': ['super_admin'],
  '/admin/users/new': ['super_admin'],
  '/teacher': ['teacher'],
  '/student': ['student'],
  '/parent': ['parent'],
  '/admission': ['admission_officer'],
  '/finance': ['finance_manager'],
  '/library': ['librarian'],
  '/transport': ['transport_manager'],
  '/hostel': ['hostel_manager'],
  '/analytics': ['super_admin', 'admin'],
  '/settings': ['super_admin', 'admin'],
  '/students': ['super_admin', 'admin', 'teacher'],
  '/admin/students/new': ['super_admin', 'admin', 'admission_officer'],
  '/teachers': ['super_admin', 'admin'],
  '/classes': ['super_admin', 'admin', 'teacher'],
  '/attendance': ['super_admin', 'admin', 'teacher'],
  '/grades': ['super_admin', 'admin', 'teacher'],
  '/exams': ['super_admin', 'admin', 'teacher'],
  '/timetable': ['super_admin', 'admin', 'teacher'],
};

// Public routes that don't require authentication
const publicRoutes = ['/login', '/api/auth/login', '/api/auth/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // Allow API routes (except auth routes which are handled above)
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Check if route requires authentication
  const isProtectedRoute = Object.keys(protectedRoutes).some(route =>
    pathname.startsWith(route)
  );

  if (isProtectedRoute) {
    // In a real application, you would verify the JWT token here
    // Since we're using localStorage for tokens (client-side), we can't check them in middleware
    // The individual pages will handle authentication checks
    // For now, we'll allow access and let the pages handle redirects

    // Note: In production, you should use httpOnly cookies for tokens
    // and verify them here in the middleware
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
