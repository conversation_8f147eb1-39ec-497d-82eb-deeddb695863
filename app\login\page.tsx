"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  GraduationCap,
  Users,
  UserCheck,
  Shield,
  ShieldCheck,
  DollarSign,
  BookOpen,
  Bus,
  Building
} from "lucide-react";
import { toast } from "sonner";

const userTypes = [
  {
    id: "super_admin",
    title: "Super Admin",
    description: "Full system access and control",
    icon: ShieldCheck,
    color: "bg-red-500 hover:bg-red-600",
    credentials: { email: "<EMAIL>", password: "admin123" }
  },
  {
    id: "admin",
    title: "Admin/Principal",
    description: "School administration and management",
    icon: Shield,
    color: "bg-purple-500 hover:bg-purple-600",
    credentials: { email: "<EMAIL>", password: "principal123" }
  },
  {
    id: "teacher",
    title: "Teacher",
    description: "Class management and student evaluation",
    icon: GraduationCap,
    color: "bg-blue-500 hover:bg-blue-600",
    credentials: { email: "<EMAIL>", password: "teacher123" }
  },
  {
    id: "student",
    title: "Student",
    description: "Access grades, assignments, and schedules",
    icon: Users,
    color: "bg-green-500 hover:bg-green-600",
    credentials: { email: "<EMAIL>", password: "student123" }
  },
  {
    id: "parent",
    title: "Parent",
    description: "Monitor child's academic progress",
    icon: UserCheck,
    color: "bg-orange-500 hover:bg-orange-600",
    credentials: { email: "<EMAIL>", password: "parent123" }
  },
  {
    id: "admission_officer",
    title: "Admission Officer",
    description: "Student admissions and registration",
    icon: Users,
    color: "bg-teal-500 hover:bg-teal-600",
    credentials: { email: "<EMAIL>", password: "admission123" }
  },
  {
    id: "finance_manager",
    title: "Finance Manager",
    description: "Fee management and financial operations",
    icon: DollarSign,
    color: "bg-yellow-500 hover:bg-yellow-600",
    credentials: { email: "<EMAIL>", password: "finance123" }
  },
  {
    id: "librarian",
    title: "Librarian",
    description: "Library management and book operations",
    icon: BookOpen,
    color: "bg-indigo-500 hover:bg-indigo-600",
    credentials: { email: "<EMAIL>", password: "librarian123" }
  },
  {
    id: "transport_manager",
    title: "Transport Manager",
    description: "Vehicle and route management",
    icon: Bus,
    color: "bg-cyan-500 hover:bg-cyan-600",
    credentials: { email: "<EMAIL>", password: "transport123" }
  },
  {
    id: "hostel_manager",
    title: "Hostel Manager",
    description: "Hostel and accommodation management",
    icon: Building,
    color: "bg-pink-500 hover:bg-pink-600",
    credentials: { email: "<EMAIL>", password: "hostel123" }
  },
];

export default function LoginPage() {
  const [selectedUserType, setSelectedUserType] = useState<string | null>(null);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleUserTypeSelect = (userType: any) => {
    setSelectedUserType(userType.id);
    setEmail(userType.credentials.email);
    setPassword(userType.credentials.password);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedUserType || !email || !password) {
      toast.error("Please select a user type and enter credentials");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          role: selectedUserType,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Login failed");
      }

      // Store user data and token
      localStorage.setItem("user", JSON.stringify(data.data.user));
      localStorage.setItem("token", data.data.token);

      // Also set cookie for middleware (expires in 7 days)
      document.cookie = `token=${data.data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;

      toast.success("Login successful!");

      // Redirect based on user role
      const redirectPath = getRoleBasedRedirect(selectedUserType);

      // Add a small delay to ensure localStorage is set
      setTimeout(() => {
        router.push(redirectPath);
      }, 100);

    } catch (error: any) {
      toast.error(error.message || "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBasedRedirect = (role: string): string => {
    const redirectMap: Record<string, string> = {
      super_admin: "/admin/dashboard",
      admin: "/admin/dashboard",
      teacher: "/teacher/dashboard",
      student: "/student/dashboard",
      parent: "/parent/dashboard",
      admission_officer: "/admission/dashboard",
      finance_manager: "/finance/dashboard",
      librarian: "/library/dashboard",
      transport_manager: "/transport/dashboard",
      hostel_manager: "/hostel/dashboard",
    };

    return redirectMap[role] || "/";
  };

  if (selectedUserType) {
    const userType = userTypes.find(ut => ut.id === selectedUserType);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              {userType && <userType.icon className="h-8 w-8 text-blue-600" />}
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {userType?.title} Login
            </CardTitle>
            <p className="text-gray-600 mt-2">{userType?.description}</p>
          </CardHeader>
          <CardContent className="pt-0">
            <form onSubmit={handleLogin} className="space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="h-11 text-gray-900 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Password
                </label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="h-11 text-gray-900 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="flex gap-3 pt-2">
                <Button
                  type="submit"
                  className="flex-1 h-11 bg-blue-600 hover:bg-blue-700 text-white font-semibold"
                  disabled={isLoading}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="h-11 px-6 border-gray-300 text-gray-700 hover:bg-gray-50"
                  onClick={() => setSelectedUserType(null)}
                >
                  Back
                </Button>
              </div>
            </form>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm font-semibold text-blue-800 mb-1">Demo Credentials:</p>
              <p className="text-sm text-blue-700">Email: {userType?.credentials.email}</p>
              <p className="text-sm text-blue-700">Password: {userType?.credentials.password}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 pt-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            School Management System
          </h1>
          <p className="text-xl text-gray-600">
            Select your role to access the system
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {userTypes.map((userType) => (
            <Card
              key={userType.id}
              className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 shadow-lg bg-white"
              onClick={() => handleUserTypeSelect(userType)}
            >
              <CardContent className="p-6 text-center">
                <div className={`mx-auto mb-4 w-16 h-16 ${userType.color} rounded-full flex items-center justify-center transition-all duration-300 shadow-md`}>
                  <userType.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {userType.title}
                </h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {userType.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto shadow-lg border-0 bg-white">
            <CardContent className="p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6">System Features</h3>
              <div className="grid gap-6 md:grid-cols-2 text-sm">
                <div className="text-left">
                  <h4 className="font-bold text-gray-900 mb-3 text-base">Academic Management</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      Student Information System
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      Grade & Attendance Tracking
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      Examination Management
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      Timetable Management
                    </li>
                  </ul>
                </div>
                <div className="text-left">
                  <h4 className="font-bold text-gray-900 mb-3 text-base">Administrative</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Fee Management
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Library Management
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Transport Management
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Hostel Management
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
