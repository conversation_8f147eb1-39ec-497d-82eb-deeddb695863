"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChevronDown,
  ChevronRight,
  GraduationCap,
  BookOpen,
  Building2,
  Users,
  Loader2,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Types for hierarchical data
interface InstitutionConfig {
  institutionType: "school" | "college";
  institutionName: string;
  currentAcademicYear: string;
}

interface AcademicStream {
  id: string;
  name: string;
  code: string;
  category: string;
  description?: string;
  duration: number;
  department?: string;
  branches?: AcademicBranch[];
}

interface AcademicBranch {
  id: string;
  streamId: string;
  name: string;
  code: string;
  shortName: string;
  description?: string;
  duration: number;
  totalSeats: number;
  programs?: AcademicProgram[];
}

interface AcademicProgram {
  id: string;
  name: string;
  code: string;
  type: string;
  streamId?: string;
  branchId?: string;
  duration: number;
  totalSemesters?: number;
  department?: string;
  admissionStatus: "open" | "closed" | "waitlist";
  totalSeats: number;
  availableSeats?: number;
}

interface HierarchicalData {
  streams: AcademicStream[];
  programs: AcademicProgram[];
  institutionConfig: InstitutionConfig;
}

interface SelectedProgram {
  programId: string;
  programName: string;
  programCode: string;
  streamId?: string;
  streamName?: string;
  branchId?: string;
  branchName?: string;
  type: string;
  duration: number;
  totalSeats: number;
  availableSeats?: number;
}

interface HierarchicalProgramSelectorProps {
  onSelectionChange: (selection: SelectedProgram | null) => void;
  selectedProgramId?: string;
  disabled?: boolean;
  className?: string;
}

export function HierarchicalProgramSelector({
  onSelectionChange,
  selectedProgramId,
  disabled = false,
  className,
}: HierarchicalProgramSelectorProps) {
  const [data, setData] = useState<HierarchicalData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedStreams, setExpandedStreams] = useState<Set<string>>(new Set());
  const [expandedBranches, setExpandedBranches] = useState<Set<string>>(new Set());
  const [selectedProgram, setSelectedProgram] = useState<SelectedProgram | null>(null);

  // Load hierarchical data
  useEffect(() => {
    loadHierarchicalData();
  }, []);

  // Update selection when selectedProgramId prop changes
  useEffect(() => {
    if (selectedProgramId && data) {
      const program = findProgramById(selectedProgramId);
      if (program) {
        setSelectedProgram(program);
        // Auto-expand relevant sections
        if (program.streamId) {
          setExpandedStreams(prev => new Set([...prev, program.streamId!]));
        }
        if (program.branchId) {
          setExpandedBranches(prev => new Set([...prev, program.branchId!]));
        }
      }
    }
  }, [selectedProgramId, data]);

  const loadHierarchicalData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load institution config
      const configResponse = await fetch("/api/institution-config");
      if (!configResponse.ok) {
        throw new Error("Failed to load institution configuration");
      }
      const configData = await configResponse.json();
      const institutionConfig = configData.data;

      if (!institutionConfig) {
        throw new Error("Institution not configured");
      }

      // Load hierarchical program data
      const hierarchicalResponse = await fetch("/api/academic-programs/hierarchical");
      if (!hierarchicalResponse.ok) {
        throw new Error("Failed to load program hierarchy");
      }
      const hierarchicalData = await hierarchicalResponse.json();

      setData({
        ...hierarchicalData.data,
        institutionConfig,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const findProgramById = (programId: string): SelectedProgram | null => {
    if (!data) return null;

    // For school programs
    if (data.institutionConfig.institutionType === "school") {
      const program = data.programs.find(p => p.id === programId);
      if (program) {
        return {
          programId: program.id,
          programName: program.name,
          programCode: program.code,
          type: program.type,
          duration: program.duration,
          totalSeats: program.totalSeats,
          availableSeats: program.availableSeats,
        };
      }
    } else {
      // For college programs - search through streams and branches
      for (const stream of data.streams) {
        if (stream.branches) {
          for (const branch of stream.branches) {
            if (branch.programs) {
              const program = branch.programs.find(p => p.id === programId);
              if (program) {
                return {
                  programId: program.id,
                  programName: program.name,
                  programCode: program.code,
                  streamId: stream.id,
                  streamName: stream.name,
                  branchId: branch.id,
                  branchName: branch.name,
                  type: program.type,
                  duration: program.duration,
                  totalSeats: program.totalSeats,
                  availableSeats: program.availableSeats,
                };
              }
            }
          }
        }
      }
    }

    return null;
  };

  const handleProgramSelect = (program: AcademicProgram, stream?: AcademicStream, branch?: AcademicBranch) => {
    const selection: SelectedProgram = {
      programId: program.id,
      programName: program.name,
      programCode: program.code,
      streamId: stream?.id,
      streamName: stream?.name,
      branchId: branch?.id,
      branchName: branch?.name,
      type: program.type,
      duration: program.duration,
      totalSeats: program.totalSeats,
      availableSeats: program.availableSeats,
    };

    setSelectedProgram(selection);
    onSelectionChange(selection);
  };

  const toggleStreamExpansion = (streamId: string) => {
    setExpandedStreams(prev => {
      const newSet = new Set(prev);
      if (newSet.has(streamId)) {
        newSet.delete(streamId);
      } else {
        newSet.add(streamId);
      }
      return newSet;
    });
  };

  const toggleBranchExpansion = (branchId: string) => {
    setExpandedBranches(prev => {
      const newSet = new Set(prev);
      if (newSet.has(branchId)) {
        newSet.delete(branchId);
      } else {
        newSet.add(branchId);
      }
      return newSet;
    });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            Loading Programs...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-red-200", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Error Loading Programs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadHierarchicalData} variant="outline" size="sm">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GraduationCap className="h-5 w-5" />
          Available Classes
        </CardTitle>
        {selectedProgram && (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle2 className="h-4 w-4" />
            Selected: {selectedProgram.programName}
            {selectedProgram.streamName && ` (${selectedProgram.streamName})`}
            {selectedProgram.branchName && ` - ${selectedProgram.branchName}`}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <SchoolProgramsView
          programs={data.programs}
          selectedProgramId={selectedProgram?.programId}
          onProgramSelect={handleProgramSelect}
          disabled={disabled}
        />
      </CardContent>
    </Card>
  );
}

// School Programs View Component
interface SchoolProgramsViewProps {
  programs: AcademicProgram[];
  selectedProgramId?: string;
  onProgramSelect: (program: AcademicProgram) => void;
  disabled: boolean;
}

function SchoolProgramsView({
  programs,
  selectedProgramId,
  onProgramSelect,
  disabled,
}: SchoolProgramsViewProps) {
  if (programs.length === 0) {
    return (
      <div className="text-center py-8">
        <GraduationCap className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-600">No classes available</p>
        <p className="text-sm text-gray-500">Please contact administration to add classes</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {programs.map((program) => (
        <div
          key={program.id}
          className={cn(
            "p-4 border rounded-lg cursor-pointer transition-all duration-200",
            "hover:shadow-md hover:border-blue-300",
            selectedProgramId === program.id
              ? "border-blue-500 bg-blue-50 shadow-md"
              : "border-gray-200",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => !disabled && onProgramSelect(program)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <GraduationCap className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">{program.name}</h4>
                <p className="text-sm text-gray-600">Code: {program.code}</p>
                {program.department && (
                  <p className="text-xs text-gray-500">{program.department}</p>
                )}
              </div>
            </div>
            <div className="text-right">
              <Badge
                variant={program.admissionStatus === "open" ? "default" : "secondary"}
                className="mb-1"
              >
                {program.admissionStatus}
              </Badge>
              <p className="text-sm text-gray-600">
                {program.availableSeats !== undefined
                  ? `${program.availableSeats}/${program.totalSeats} seats`
                  : `${program.totalSeats} seats`}
              </p>
              <p className="text-xs text-gray-500">{program.duration} year(s)</p>
            </div>
          </div>
          {selectedProgramId === program.id && (
            <div className="mt-3 pt-3 border-t border-blue-200">
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <CheckCircle2 className="h-4 w-4" />
                Selected Class
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}


