import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq, count, and, desc, sql, like, or } from "drizzle-orm";
import { createUserSchema, updateUserSchema, bulkCreateUsersSchema, resetPasswordSchema, bulkPasswordResetSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

// Helper function to generate random password
function generatePassword(length: number = 12): string {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

// Helper function to get default permissions based on role
function getDefaultPermissions(role: string): string[] {
  const permissionMap: Record<string, string[]> = {
    super_admin: ["all"],
    admin: ["students", "teachers", "classes", "reports", "settings"],
    teacher: ["classes", "grades", "attendance", "assignments"],
    student: ["view_grades", "view_attendance", "view_assignments", "view_timetable"],
    parent: ["view_child_grades", "view_child_attendance", "view_child_reports"],
    admission_officer: ["student_admission", "student_registration", "student_records"],
    finance_manager: ["fee_management", "payments", "financial_reports", "fee_structures"],
    librarian: ["library_management", "book_issue", "book_return", "library_reports"],
    transport_manager: ["transport_management", "route_planning", "vehicle_tracking"],
    hostel_manager: ["hostel_management", "room_allocation", "hostel_reports"],
  };

  return permissionMap[role] || [];
}

const app = new Hono()
  // Get all users with filtering and pagination
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "10");
      const search = c.req.query("search") || "";
      const role = c.req.query("role") || "";
      const status = c.req.query("status") || "";
      const offset = (page - 1) * limit;

      // Build where conditions
      let whereConditions = [];

      if (search) {
        whereConditions.push(
          or(
            like(users.firstName, `%${search}%`),
            like(users.lastName, `%${search}%`),
            like(users.email, `%${search}%`)
          )
        );
      }

      if (role) {
        whereConditions.push(eq(users.role, role as any));
      }

      if (status) {
        whereConditions.push(eq(users.status, status as any));
      }

      const whereClause = whereConditions.length > 0
        ? and(...whereConditions)
        : undefined;

      // Get users with pagination
      const usersList = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          phone: users.phone,
          role: users.role,
          status: users.status,
          lastLogin: users.lastLogin,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(users)
        .where(whereClause)
        .orderBy(desc(users.createdAt))
        .limit(limit)
        .offset(offset);

      // Get total count
      const [totalCount] = await db
        .select({ count: count() })
        .from(users)
        .where(whereClause);

      return c.json({
        data: usersList,
        pagination: {
          page,
          limit,
          total: totalCount.count,
          totalPages: Math.ceil(totalCount.count / limit),
        },
      });
    } catch (error) {
      console.error("Error fetching users:", error);
      return c.json({ error: "Failed to fetch users" }, 500);
    }
  })

  // Get user by ID
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [user] = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          phone: users.phone,
          role: users.role,
          status: users.status,
          lastLogin: users.lastLogin,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(users)
        .where(eq(users.id, id));

      if (!user) {
        return c.json({ error: "User not found" }, 404);
      }

      return c.json({ data: user });
    } catch (error) {
      console.error("Error fetching user:", error);
      return c.json({ error: "Failed to fetch user" }, 500);
    }
  })

  // Create new user
  .post(
    "/",
    zValidator("json", createUserSchema),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Check if user already exists
        const [existingUser] = await db
          .select()
          .from(users)
          .where(eq(users.email, values.email));

        if (existingUser) {
          return c.json({ error: "User with this email already exists" }, 400);
        }

        // Generate password if needed
        let password = values.password;
        if (values.generatePassword || !password) {
          password = generatePassword();
        }

        // Create user
        const [newUser] = await db
          .insert(users)
          .values({
            email: values.email,
            password: password, // In production, hash this password
            firstName: values.firstName,
            lastName: values.lastName,
            phone: values.phone,
            role: values.role,
            status: values.status || "active",
          })
          .returning({
            id: users.id,
            email: users.email,
            firstName: users.firstName,
            lastName: users.lastName,
            phone: users.phone,
            role: users.role,
            status: users.status,
            createdAt: users.createdAt,
            updatedAt: users.updatedAt,
          });

        // Return user data with generated password (for admin to share)
        return c.json({
          data: {
            ...newUser,
            generatedPassword: values.generatePassword ? password : undefined,
          },
        }, 201);
      } catch (error) {
        console.error("Error creating user:", error);
        return c.json({ error: "Failed to create user" }, 500);
      }
    }
  )

  // Update user
  .put(
    "/:id",
    zValidator("json", updateUserSchema.omit({ id: true })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // Check if user exists
        const [existingUser] = await db
          .select()
          .from(users)
          .where(eq(users.id, id));

        if (!existingUser) {
          return c.json({ error: "User not found" }, 404);
        }

        // Check for email conflicts if email is being updated
        if (values.email && values.email !== existingUser.email) {
          const [emailConflict] = await db
            .select()
            .from(users)
            .where(and(eq(users.email, values.email), eq(users.id, id)));

          if (emailConflict) {
            return c.json({ error: "Email already in use" }, 400);
          }
        }

        // Update user
        const updateData: any = {
          updatedAt: new Date(),
        };

        if (values.email) updateData.email = values.email;
        if (values.firstName) updateData.firstName = values.firstName;
        if (values.lastName) updateData.lastName = values.lastName;
        if (values.phone) updateData.phone = values.phone;
        if (values.role) updateData.role = values.role;
        if (values.status) updateData.status = values.status;
        if (values.password) updateData.password = values.password; // Hash in production

        const [updatedUser] = await db
          .update(users)
          .set(updateData)
          .where(eq(users.id, id))
          .returning({
            id: users.id,
            email: users.email,
            firstName: users.firstName,
            lastName: users.lastName,
            phone: users.phone,
            role: users.role,
            status: users.status,
            lastLogin: users.lastLogin,
            createdAt: users.createdAt,
            updatedAt: users.updatedAt,
          });

        return c.json({ data: updatedUser });
      } catch (error) {
        console.error("Error updating user:", error);
        return c.json({ error: "Failed to update user" }, 500);
      }
    }
  )

  // Deactivate user (soft delete)
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Check if user exists
      const [existingUser] = await db
        .select()
        .from(users)
        .where(eq(users.id, id));

      if (!existingUser) {
        return c.json({ error: "User not found" }, 404);
      }

      // Deactivate user
      await db
        .update(users)
        .set({
          status: "inactive",
          updatedAt: new Date(),
        })
        .where(eq(users.id, id));

      return c.json({ message: "User deactivated successfully" });
    } catch (error) {
      console.error("Error deactivating user:", error);
      return c.json({ error: "Failed to deactivate user" }, 500);
    }
  })

  // Bulk create users
  .post(
    "/bulk",
    zValidator("json", bulkCreateUsersSchema),
    async (c) => {
      try {
        const { users: usersToCreate, sendNotifications } = c.req.valid("json");

        const results = {
          successful: [] as any[],
          failed: [] as any[],
          generatedPasswords: [] as any[],
        };

        for (const userData of usersToCreate) {
          try {
            // Check if user already exists
            const [existingUser] = await db
              .select()
              .from(users)
              .where(eq(users.email, userData.email));

            if (existingUser) {
              results.failed.push({
                email: userData.email,
                error: "User already exists",
              });
              continue;
            }

            // Generate password if needed
            let password = userData.password;
            if (userData.generatePassword || !password) {
              password = generatePassword();
            }

            // Create user
            const [newUser] = await db
              .insert(users)
              .values({
                email: userData.email,
                password: password, // Hash in production
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                role: userData.role,
                status: userData.status || "active",
              })
              .returning({
                id: users.id,
                email: users.email,
                firstName: users.firstName,
                lastName: users.lastName,
                role: users.role,
                status: users.status,
              });

            results.successful.push(newUser);

            if (userData.generatePassword || !userData.password) {
              results.generatedPasswords.push({
                email: userData.email,
                password: password,
              });
            }
          } catch (error) {
            results.failed.push({
              email: userData.email,
              error: "Failed to create user",
            });
          }
        }

        return c.json({
          data: {
            summary: {
              total: usersToCreate.length,
              successful: results.successful.length,
              failed: results.failed.length,
            },
            results,
          },
        }, 201);
      } catch (error) {
        console.error("Error bulk creating users:", error);
        return c.json({ error: "Failed to bulk create users" }, 500);
      }
    }
  )

  // Reset user password (Super Admin only)
  .post(
    "/:id/reset-password",
    zValidator("json", resetPasswordSchema.omit({ userId: true })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // Check if user exists
        const [existingUser] = await db
          .select()
          .from(users)
          .where(eq(users.id, id));

        if (!existingUser) {
          return c.json({ error: "User not found" }, 404);
        }

        // Generate password if needed
        let newPassword = values.newPassword;
        if (values.generatePassword || !newPassword) {
          newPassword = generatePassword();
        }

        // Update user password
        const [updatedUser] = await db
          .update(users)
          .set({
            password: newPassword, // Hash in production
            passwordChangedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(users.id, id))
          .returning({
            id: users.id,
            email: users.email,
            firstName: users.firstName,
            lastName: users.lastName,
            role: users.role,
          });

        return c.json({
          data: {
            user: updatedUser,
            generatedPassword: values.generatePassword || !values.newPassword ? newPassword : undefined,
          },
        });
      } catch (error) {
        console.error("Reset password error:", error);
        return c.json({ error: "Failed to reset password" }, 500);
      }
    }
  )

  // Bulk password reset (Super Admin only)
  .post(
    "/bulk-reset-password",
    zValidator("json", bulkPasswordResetSchema),
    async (c) => {
      try {
        const { userIds, generatePasswords, sendNotifications } = c.req.valid("json");

        const results = {
          successful: [] as any[],
          failed: [] as any[],
          generatedPasswords: [] as any[],
        };

        for (const userId of userIds) {
          try {
            // Check if user exists
            const [existingUser] = await db
              .select()
              .from(users)
              .where(eq(users.id, userId));

            if (!existingUser) {
              results.failed.push({
                userId,
                error: "User not found",
              });
              continue;
            }

            // Generate new password
            const newPassword = generatePassword();

            // Update user password
            const [updatedUser] = await db
              .update(users)
              .set({
                password: newPassword, // Hash in production
                passwordChangedAt: new Date(),
                updatedAt: new Date(),
              })
              .where(eq(users.id, userId))
              .returning({
                id: users.id,
                email: users.email,
                firstName: users.firstName,
                lastName: users.lastName,
                role: users.role,
              });

            results.successful.push(updatedUser);

            if (generatePasswords) {
              results.generatedPasswords.push({
                userId,
                email: updatedUser.email,
                password: newPassword,
              });
            }
          } catch (error) {
            results.failed.push({
              userId,
              error: "Failed to reset password",
            });
          }
        }

        const summary = {
          total: userIds.length,
          successful: results.successful.length,
          failed: results.failed.length,
        };

        return c.json({ data: results, summary }, 201);
      } catch (error) {
        console.error("Bulk password reset error:", error);
        return c.json({ error: "Failed to bulk reset passwords" }, 500);
      }
    }
  );

export default app;
