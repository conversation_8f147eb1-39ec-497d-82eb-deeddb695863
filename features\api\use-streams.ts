import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Types for stream management
export interface AcademicStream {
  id: string;
  name: string;
  code: string;
  category: "engineering" | "diploma" | "pharmacy" | "science" | "commerce" | "arts" | "management" | "medical" | "other";
  institutionType: "school" | "college";
  description?: string;
  eligibilityCriteria?: string;
  duration: number;
  department?: string;
  isActive: boolean;
  displayOrder: number;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
}

export interface CreateStreamData {
  name: string;
  code: string;
  type: "engineering" | "science" | "commerce" | "arts" | "management" | "medical" | "other";
  institutionType: "school" | "college";
  description?: string;
  eligibilityCriteria?: string;
  duration?: number;
  department?: string;
  displayOrder?: number;
}

export interface UpdateStreamData {
  name?: string;
  code?: string;
  type?: "engineering" | "science" | "commerce" | "arts" | "management" | "medical" | "other";
  description?: string;
  eligibilityCriteria?: string;
  duration?: number;
  department?: string;
  isActive?: boolean;
  displayOrder?: number;
}

// Get all streams
export const useGetStreams = (params?: {
  institutionType?: "school" | "college";
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: ["streams", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      
      if (params?.institutionType) {
        searchParams.append("institutionType", params.institutionType);
      }
      
      if (params?.isActive !== undefined) {
        searchParams.append("isActive", params.isActive.toString());
      }

      const response = await client.api["academic-streams"].$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch streams");
      }

      const data = await response.json();
      return data.data as AcademicStream[];
    },
  });
};

// Get single stream
export const useGetStream = (id: string) => {
  return useQuery({
    queryKey: ["stream", id],
    queryFn: async () => {
      const response = await client.api["academic-streams"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch stream");
      }

      const data = await response.json();
      return data.data as AcademicStream;
    },
    enabled: !!id,
  });
};

// Create stream
export const useCreateStream = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateStreamData) => {
      const response = await client.api["academic-streams"].$post({
        json: data,
      });

      if (!response.ok) {
        const error = await response.json() as { error?: string };
        throw new Error(error.error || "Failed to create stream");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["streams"] });
      toast.success("Academic stream created successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Update stream
export const useUpdateStream = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateStreamData }) => {
      const response = await client.api["academic-streams"][":id"].$put({
        param: { id },
        json: data,
      });

      if (!response.ok) {
        const error = await response.json() as { error?: string };
        throw new Error(error.error || "Failed to update stream");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["streams"] });
      toast.success("Academic stream updated successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Delete stream
export const useDeleteStream = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["academic-streams"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const error = await response.json() as { error?: string };
        throw new Error(error.error || "Failed to delete stream");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["streams"] });
      toast.success("Academic stream deleted successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Get streams by institution type (helper hook)
export const useGetStreamsByInstitution = (institutionType?: "school" | "college") => {
  return useGetStreams({
    institutionType,
    isActive: true,
  });
};

// Get college streams (helper hook)
export const useGetCollegeStreams = () => {
  return useGetStreamsByInstitution("college");
};

// Get school streams (helper hook)
export const useGetSchoolStreams = () => {
  return useGetStreamsByInstitution("school");
};

// Stream categories for different institution types
export const getStreamCategories = (institutionType: "school" | "college") => {
  if (institutionType === "school") {
    return [
      { value: "science", label: "Science" },
      { value: "commerce", label: "Commerce" },
      { value: "arts", label: "Arts" },
    ];
  } else {
    return [
      { value: "engineering", label: "Engineering" },
      { value: "diploma", label: "Diploma" },
      { value: "pharmacy", label: "Pharmacy" },
      { value: "medical", label: "Medical" },
      { value: "management", label: "Management" },
      { value: "science", label: "Science" },
      { value: "commerce", label: "Commerce" },
      { value: "arts", label: "Arts" },
      { value: "other", label: "Other" },
    ];
  }
};

// Predefined branches for different stream categories
export const getPredefinedBranches = (category: string) => {
  switch (category) {
    case "engineering":
      return [
        { name: "Computer Science Engineering", code: "CSE", shortName: "Computer Science" },
        { name: "Mechanical Engineering", code: "ME", shortName: "Mechanical" },
        { name: "Civil Engineering", code: "CE", shortName: "Civil" },
        { name: "Electrical Engineering", code: "EE", shortName: "Electrical" },
        { name: "Electronics & Communication", code: "ECE", shortName: "Electronics" },
        { name: "Information Technology", code: "IT", shortName: "IT" },
      ];
    case "diploma":
      return [
        { name: "Computer Science Diploma", code: "CSD", shortName: "CS Diploma" },
        { name: "Mechanical Diploma", code: "MED", shortName: "Mech Diploma" },
        { name: "Civil Diploma", code: "CED", shortName: "Civil Diploma" },
        { name: "Electrical Diploma", code: "EED", shortName: "Electrical Diploma" },
      ];
    case "pharmacy":
      return [
        { name: "Bachelor of Pharmacy", code: "BPHAR", shortName: "B.Pharm" },
        { name: "Diploma in Pharmacy", code: "DPHAR", shortName: "D.Pharm" },
        { name: "Master of Pharmacy", code: "MPHAR", shortName: "M.Pharm" },
      ];
    default:
      return [];
  }
};
