import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { loginSchema, registerSchema } from "@/lib/schemas";
import { mockUsers } from "@/lib/mock-db";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .post(
    "/login",
    zValidator("json", loginSchema),
    async (c) => {
      const { email, password, role } = c.req.valid("json");

      // Find user by email
      const user = mockUsers.find((u) => u.email === email);

      if (!user) {
        return c.json({ error: "Invalid credentials" }, 401);
      }

      // Check password (in real app, compare hashed passwords)
      if (user.password !== password) {
        return c.json({ error: "Invalid credentials" }, 401);
      }

      // Check role if specified
      if (role && user.role !== role) {
        return c.json({ error: "Invalid role for this user" }, 403);
      }

      // Check if user is active
      if (!user.isActive) {
        return c.json({ error: "Account is deactivated" }, 403);
      }

      // Update last login
      user.lastLogin = new Date().toISOString();

      // Return user data (excluding password)
      const { password: _, ...userWithoutPassword } = user;

      return c.json({
        data: {
          user: userWithoutPassword,
          token: `mock-jwt-token-${user.id}`, // In real app, generate JWT
        },
      });
    }
  )
  .post(
    "/register",
    zValidator("json", registerSchema),
    async (c) => {
      const values = c.req.valid("json");

      // Check if user already exists
      const existingUser = mockUsers.find((u) => u.email === values.email);
      if (existingUser) {
        return c.json({ error: "User already exists" }, 409);
      }

      // Create new user
      const newUser = {
        id: generateId(),
        email: values.email,
        password: values.password, // In real app, hash the password
        role: values.role,
        firstName: values.firstName,
        lastName: values.lastName,
        phone: values.phone,
        avatar: "",
        isActive: true,
        lastLogin: new Date().toISOString(),
        permissions: getDefaultPermissions(values.role),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockUsers.push(newUser);

      // Return user data (excluding password)
      const { password: _, ...userWithoutPassword } = newUser;

      return c.json({
        data: {
          user: userWithoutPassword,
          token: `mock-jwt-token-${newUser.id}`,
        },
      }, 201);
    }
  )
  .post("/logout", async (c) => {
    // In a real app, invalidate the JWT token
    return c.json({ message: "Logged out successfully" });
  })
  .get("/me", async (c) => {
    // In a real app, verify JWT token from Authorization header
    const authHeader = c.req.header("Authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const token = authHeader.substring(7);

    // Extract user ID from mock token
    const userId = token.replace("mock-jwt-token-", "");

    const user = mockUsers.find((u) => u.id === userId);

    if (!user || !user.isActive) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Return user data (excluding password)
    const { password: _, ...userWithoutPassword } = user;

    return c.json({ data: userWithoutPassword });
  })
  .post("/logout", async (c) => {
    // In a real app, you would:
    // 1. Verify the JWT token
    // 2. Add the token to a blacklist/revoked tokens list
    // 3. Clear any server-side session data
    // 4. Log the logout event

    const authHeader = c.req.header("Authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return c.json({ error: "No token provided" }, 401);
    }

    const token = authHeader.substring(7);

    // Extract user ID from mock token for logging
    const userId = token.replace("mock-jwt-token-", "");

    const user = mockUsers.find((u) => u.id === userId);

    if (user) {
      // Log the logout event (in real app, save to database)
      console.log(`User ${user.email} logged out at ${new Date().toISOString()}`);
    }

    return c.json({
      message: "Logged out successfully",
      data: { success: true }
    });
  });

// Helper function to get default permissions based on role
function getDefaultPermissions(role: string): string[] {
  const permissionMap: Record<string, string[]> = {
    super_admin: ["all"],
    admin: ["students", "teachers", "classes", "reports", "settings"],
    teacher: ["classes", "grades", "attendance", "assignments"],
    student: ["view_grades", "view_attendance", "view_assignments", "view_timetable"],
    parent: ["view_child_grades", "view_child_attendance", "view_child_reports"],
    admission_officer: ["student_admission", "student_registration", "student_records"],
    finance_manager: ["fee_management", "payments", "financial_reports", "fee_structures"],
    librarian: ["library_management", "book_issue", "book_return", "library_reports"],
    transport_manager: ["transport_management", "routes", "vehicles", "transport_reports"],
    hostel_manager: ["hostel_management", "room_allocation", "hostel_reports"],
  };

  return permissionMap[role] || [];
}

export default app;
