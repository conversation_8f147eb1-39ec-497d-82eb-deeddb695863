"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Building2, Save, Settings } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

interface InstitutionConfig {
  id?: string;
  institutionType: "school" | "college";
  institutionName: string;
  institutionCode: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  establishedYear?: number;
  affiliation?: string;
  currentAcademicYear: string;
  sessionStartMonth: number;
  sessionEndMonth: number;
  gradingSystem: string;
  currency: string;
  timezone: string;
  dateFormat: string;
  language: string;
  isConfigured: boolean;
}

export default function InstitutionConfigPage() {
  const [user, setUser] = useState<any>(null);
  const [config, setConfig] = useState<InstitutionConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const router = useRouter();

  const [formData, setFormData] = useState<Partial<InstitutionConfig>>({
    institutionType: "school", // Fixed to school only
    institutionName: "",
    institutionCode: "",
    address: "",
    phone: "",
    email: "",
    website: "",
    establishedYear: new Date().getFullYear(),
    affiliation: "",
    currentAcademicYear: "2024-25",
    sessionStartMonth: 4,
    sessionEndMonth: 3,
    gradingSystem: "Grade-based (A-F)",
    currency: "INR",
    timezone: "Asia/Kolkata",
    dateFormat: "DD/MM/YYYY",
    language: "English",
  });

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchInstitutionConfig();
  }, [router]);

  const fetchInstitutionConfig = async () => {
    try {
      const response = await fetch("/api/institution-config");
      const result = await response.json();

      if (response.ok) {
        if (result.data) {
          setConfig(result.data);
          setFormData(result.data);
          setIsConfigured(result.isConfigured);
        }
      } else {
        console.error("Error fetching config:", result.error);
      }
    } catch (error) {
      console.error("Error fetching institution config:", error);
      toast.error("Failed to fetch institution configuration");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const url = config ? `/api/institution-config/${config.id}` : "/api/institution-config";
      const method = config ? "PUT" : "POST";

      const payload = {
        ...formData,
        [config ? "updatedBy" : "configuredBy"]: user.id,
      };

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setConfig(result.data);
        setIsConfigured(true);

        // Refresh the page to update navigation if institution type changed
        if (!config || config.institutionType !== formData.institutionType) {
          window.location.reload();
        }
      } else {
        // Handle validation errors properly
        let errorMessage = "Failed to save configuration";

        if (result.error) {
          if (typeof result.error === 'string') {
            errorMessage = result.error;
          } else if (result.error.issues && Array.isArray(result.error.issues)) {
            // Handle Zod validation errors
            errorMessage = result.error.issues.map((issue: any) => issue.message).join(", ");
          } else if (result.error.message) {
            errorMessage = result.error.message;
          }
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error saving config:", error);
      toast.error("Failed to save institution configuration");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof InstitutionConfig, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading configuration...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Building2 className="h-6 w-6" />
              School Configuration
            </h1>
            <p className="text-gray-600">
              Configure your school&apos;s basic information and settings
            </p>
          </div>
          {isConfigured && (
            <Badge variant="secondary" className="w-fit">
              <Settings className="h-3 w-3 mr-1" />
              Configured
            </Badge>
          )}
        </div>

        {!isConfigured && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Your school is not configured yet. Please complete the configuration to enable all features.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* School Information */}
          <Card>
            <CardHeader>
              <CardTitle>School Information</CardTitle>
              <CardDescription>
                This system is designed specifically for school management (Nursery to Grade 12).
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800">
                  <Building2 className="h-5 w-5" />
                  <span className="font-medium">School Management System</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  Configured for comprehensive school operations from Nursery to Grade 12
                </p>
              </div>
              {/* Hidden field to maintain database compatibility */}
              <input type="hidden" name="institutionType" value="school" />
            </CardContent>
          </Card>

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>School Details</CardTitle>
              <CardDescription>
                Enter your school&apos;s basic information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="institutionName">School Name *</Label>
                  <Input
                    id="institutionName"
                    value={formData.institutionName}
                    onChange={(e) => handleInputChange("institutionName", e.target.value)}
                    placeholder="Enter school name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="institutionCode">School Code *</Label>
                  <Input
                    id="institutionCode"
                    value={formData.institutionCode}
                    onChange={(e) => handleInputChange("institutionCode", e.target.value)}
                    placeholder="Enter school code (e.g., SCH001)"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">School Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter complete school address"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="Enter email address"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    type="url"
                    value={formData.website}
                    onChange={(e) => handleInputChange("website", e.target.value)}
                    placeholder="Enter website URL"
                  />
                </div>
                <div>
                  <Label htmlFor="currentAcademicYear">Current Academic Year *</Label>
                  <Input
                    id="currentAcademicYear"
                    value={formData.currentAcademicYear}
                    onChange={(e) => handleInputChange("currentAcademicYear", e.target.value)}
                    placeholder="e.g., 2024-25"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={isSaving}>
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? "Saving..." : isConfigured ? "Update Configuration" : "Save Configuration"}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
