import {
  pgTable,
  text,
  varchar,
  integer,
  decimal,
  boolean,
  timestamp,
  date,
  time,
  pgEnum,
  uuid,
  serial,
  index,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

// Enums
export const userRoleEnum = pgEnum("user_role", [
  "super_admin",
  "admin",
  "teacher",
  "student",
  "parent",
  "admission_officer",
  "finance_manager",
  "librarian",
  "transport_manager",
  "hostel_manager"
]);

export const statusEnum = pgEnum("status", ["active", "inactive", "suspended"]);
export const attendanceStatusEnum = pgEnum("attendance_status", ["present", "absent", "late", "excused"]);
export const gradeEnum = pgEnum("grade", ["A+", "A", "A-", "B+", "B", "B-", "C+", "C", "C-", "D+", "D", "F"]);
export const paymentStatusEnum = pgEnum("payment_status", ["pending", "completed", "failed", "refunded"]);
export const paymentMethodEnum = pgEnum("payment_method", ["cash", "card", "bank_transfer", "online", "cheque"]);
export const examStatusEnum = pgEnum("exam_status", ["scheduled", "ongoing", "completed", "cancelled"]);
export const examTypeEnum = pgEnum("exam_type", ["midterm", "final", "unit_test", "practical", "oral"]);
export const vehicleTypeEnum = pgEnum("vehicle_type", ["bus", "van", "car"]);
export const vehicleStatusEnum = pgEnum("vehicle_status", ["active", "maintenance", "inactive"]);
export const hostelTypeEnum = pgEnum("hostel_type", ["boys", "girls", "mixed"]);
export const roomTypeEnum = pgEnum("room_type", ["single", "double", "triple", "dormitory"]);
export const roomStatusEnum = pgEnum("room_status", ["available", "occupied", "maintenance"]);
export const bookStatusEnum = pgEnum("book_status", ["available", "issued", "maintenance", "lost"]);
export const issueStatusEnum = pgEnum("issue_status", ["issued", "returned", "overdue"]);
export const dayEnum = pgEnum("day", ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]);

// New enums for enhanced functionality
export const studentTypeEnum = pgEnum("student_type", ["regular", "lateral", "distance"]);
export const teacherTypeEnum = pgEnum("teacher_type", ["permanent", "contract", "visiting", "guest"]);
export const programTypeEnum = pgEnum("program_type", ["school", "undergraduate", "postgraduate", "diploma", "certificate"]);
export const semesterEnum = pgEnum("semester", ["1", "2", "3", "4", "5", "6", "7", "8"]);
export const admissionStatusEnum = pgEnum("admission_status", ["open", "closed", "waitlist"]);
export const institutionTypeEnum = pgEnum("institution_type", ["school", "college"]);
export const streamTypeEnum = pgEnum("stream_type", ["science", "commerce", "arts", "engineering", "medical", "management", "other"]);
export const streamCategoryEnum = pgEnum("stream_category", ["engineering", "diploma", "pharmacy", "science", "commerce", "arts", "management", "medical", "other"]);

// Core Tables

// Institution Configuration table - System-wide settings
export const institutionConfig = pgTable("institution_config", {
  id: uuid("id").primaryKey().defaultRandom(),
  institutionType: institutionTypeEnum("institution_type").notNull().default("school"),
  institutionName: varchar("institution_name", { length: 200 }).notNull(),
  institutionCode: varchar("institution_code", { length: 20 }).notNull().unique(),
  address: text("address"),
  phone: varchar("phone", { length: 20 }),
  email: varchar("email", { length: 255 }),
  website: varchar("website", { length: 255 }),
  establishedYear: integer("established_year"),
  affiliation: varchar("affiliation", { length: 100 }),
  currentAcademicYear: varchar("current_academic_year", { length: 20 }).notNull(),
  sessionStartMonth: integer("session_start_month").notNull().default(4), // April = 4
  sessionEndMonth: integer("session_end_month").notNull().default(3), // March = 3
  gradingSystem: varchar("grading_system", { length: 50 }).default("10-point GPA"),
  currency: varchar("currency", { length: 10 }).default("INR"),
  timezone: varchar("timezone", { length: 50 }).default("Asia/Kolkata"),
  dateFormat: varchar("date_format", { length: 20 }).default("DD/MM/YYYY"),
  language: varchar("language", { length: 20 }).default("English"),
  isConfigured: boolean("is_configured").notNull().default(false),
  configuredBy: uuid("configured_by").references(() => users.id),
  configuredAt: timestamp("configured_at"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Academic Streams table - Defines main streams/categories (Engineering, Diploma, Pharmacy, etc.)
export const academicStreams = pgTable("academic_streams", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 200 }).notNull(), // "Engineering", "Diploma", "Pharmacy"
  code: varchar("code", { length: 20 }).notNull().unique(), // "ENG", "DIP", "PHAR"
  category: streamCategoryEnum("category").notNull(), // Main category
  institutionType: institutionTypeEnum("institution_type").notNull(),
  description: text("description"),
  eligibilityCriteria: text("eligibility_criteria"),
  duration: integer("duration").notNull().default(4), // Duration in years
  department: varchar("department", { length: 100 }),
  isActive: boolean("is_active").notNull().default(true),
  displayOrder: integer("display_order").default(0),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Academic Branches table - Defines specializations within streams (CS, ME, Civil, etc.)
export const academicBranches = pgTable("academic_branches", {
  id: uuid("id").primaryKey().defaultRandom(),
  streamId: uuid("stream_id").references(() => academicStreams.id).notNull(),
  name: varchar("name", { length: 200 }).notNull(), // "Computer Science", "Mechanical Engineering"
  code: varchar("code", { length: 20 }).notNull().unique(), // "CS", "ME", "CE"
  shortName: varchar("short_name", { length: 50 }).notNull(), // "CSE", "Mech", "Civil"
  description: text("description"),
  eligibilityCriteria: text("eligibility_criteria"),
  duration: integer("duration").notNull().default(4), // Inherits from stream but can be overridden
  department: varchar("department", { length: 100 }),
  totalSeats: integer("total_seats").notNull().default(60),
  isActive: boolean("is_active").notNull().default(true),
  displayOrder: integer("display_order").default(0),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Academic Sections table - Defines sections within classes
export const academicSections = pgTable("academic_sections", {
  id: uuid("id").primaryKey().defaultRandom(),
  programId: uuid("program_id").references(() => academicPrograms.id).notNull(),
  batchId: uuid("batch_id").references(() => academicBatches.id).notNull(),
  streamId: uuid("stream_id").references(() => academicStreams.id), // For backward compatibility
  branchId: uuid("branch_id").references(() => academicBranches.id), // For specializations
  name: varchar("name", { length: 10 }).notNull(), // "A", "B", "C"
  displayName: varchar("display_name", { length: 50 }).notNull(), // "Section A", "Section B"
  capacity: integer("capacity").notNull().default(30),
  occupiedSeats: integer("occupied_seats").notNull().default(0),
  availableSeats: integer("available_seats").notNull().default(30),
  classTeacherId: uuid("class_teacher_id").references(() => teachers.id),
  room: varchar("room", { length: 50 }),
  academicYear: varchar("academic_year", { length: 20 }).notNull(),
  semester: semesterEnum("semester").default("1"),
  isActive: boolean("is_active").notNull().default(true),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Academic Programs table - Defines different programs/courses offered
export const academicPrograms = pgTable("academic_programs", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 200 }).notNull(), // "Class 10", "B.Tech Computer Science", "M.Sc Physics"
  code: varchar("code", { length: 20 }).notNull().unique(), // "CLS10", "BTECHCS", "MSCPHY"
  type: programTypeEnum("type").notNull(),
  streamId: uuid("stream_id").references(() => academicStreams.id), // For college programs
  branchId: uuid("branch_id").references(() => academicBranches.id), // For specializations
  duration: integer("duration").notNull(), // Duration in years
  totalSemesters: integer("total_semesters").default(2), // Auto-calculated: duration × 2 for colleges
  description: text("description"),
  eligibilityCriteria: text("eligibility_criteria"),
  totalSeats: integer("total_seats").notNull().default(30),
  department: varchar("department", { length: 100 }),
  admissionStatus: admissionStatusEnum("admission_status").notNull().default("open"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Academic Batches table - Represents a specific batch/session of a program
export const academicBatches = pgTable("academic_batches", {
  id: uuid("id").primaryKey().defaultRandom(),
  programId: uuid("program_id").references(() => academicPrograms.id).notNull(),
  batchName: varchar("batch_name", { length: 100 }).notNull(), // "2022-2026", "2023-2024"
  startYear: integer("start_year").notNull(),
  endYear: integer("end_year").notNull(),
  currentSemester: semesterEnum("current_semester").default("1"),
  totalSeats: integer("total_seats").notNull(),
  occupiedSeats: integer("occupied_seats").notNull().default(0),
  availableSeats: integer("available_seats").notNull(), // Calculated field
  admissionStatus: admissionStatusEnum("admission_status").notNull().default("open"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Users table - Multi-role authentication
export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  password: varchar("password", { length: 255 }).notNull(),
  firstName: varchar("first_name", { length: 100 }).notNull(),
  lastName: varchar("last_name", { length: 100 }).notNull(),
  phone: varchar("phone", { length: 20 }),
  role: userRoleEnum("role").notNull(),
  status: statusEnum("status").notNull().default("active"),
  lastLogin: timestamp("last_login"),
  passwordResetToken: varchar("password_reset_token", { length: 255 }),
  passwordResetExpires: timestamp("password_reset_expires"),
  passwordChangedAt: timestamp("password_changed_at"),
  loginAttempts: integer("login_attempts").default(0),
  accountLockedUntil: timestamp("account_locked_until"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
  // Performance indexes for large datasets
  emailIdx: index("users_email_idx").on(table.email),
  roleIdx: index("users_role_idx").on(table.role),
  statusIdx: index("users_status_idx").on(table.status),
  createdAtIdx: index("users_created_at_idx").on(table.createdAt),
  lastLoginIdx: index("users_last_login_idx").on(table.lastLogin),
  // Composite indexes for common queries
  roleStatusIdx: index("users_role_status_idx").on(table.role, table.status),
  nameSearchIdx: index("users_name_search_idx").on(table.firstName, table.lastName),
}));

// User Activity Logs table for audit trail
export const userActivityLogs = pgTable("user_activity_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  action: varchar("action", { length: 100 }).notNull(), // login, logout, create_user, update_user, etc.
  targetUserId: uuid("target_user_id").references(() => users.id), // For actions on other users
  details: text("details"), // JSON string with action details
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
}, (table) => ({
  userIdIdx: index("user_activity_logs_user_id_idx").on(table.userId),
  actionIdx: index("user_activity_logs_action_idx").on(table.action),
  timestampIdx: index("user_activity_logs_timestamp_idx").on(table.timestamp),
  targetUserIdx: index("user_activity_logs_target_user_idx").on(table.targetUserId),
}));

// System Settings table for persistent configuration
export const systemSettings = pgTable("system_settings", {
  id: uuid("id").primaryKey().defaultRandom(),
  category: varchar("category", { length: 50 }).notNull(), // general, academic, notifications, security, system
  key: varchar("key", { length: 100 }).notNull(),
  value: text("value").notNull(),
  dataType: varchar("data_type", { length: 20 }).notNull().default("string"), // string, number, boolean, json
  description: text("description"),
  isSystem: boolean("is_system").notNull().default(false), // System settings vs user configurable
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
  categoryKeyIdx: index("system_settings_category_key_idx").on(table.category, table.key),
  categoryIdx: index("system_settings_category_idx").on(table.category),
  keyIdx: index("system_settings_key_idx").on(table.key),
  // Unique constraint for category + key combination
  categoryKeyUnique: index("system_settings_category_key_unique").on(table.category, table.key),
}));

// System Backups table for backup management
export const systemBackups = pgTable("system_backups", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  backupType: varchar("backup_type", { length: 50 }).notNull(), // full, incremental, manual
  status: varchar("status", { length: 20 }).notNull().default("pending"), // pending, running, completed, failed
  filePath: varchar("file_path", { length: 500 }),
  fileSize: integer("file_size"), // in bytes
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  createdBy: uuid("created_by").references(() => users.id),
  error: text("error"), // Error message if backup failed
  createdAt: timestamp("created_at").notNull().defaultNow(),
}, (table) => ({
  statusIdx: index("system_backups_status_idx").on(table.status),
  typeIdx: index("system_backups_type_idx").on(table.backupType),
  createdAtIdx: index("system_backups_created_at_idx").on(table.createdAt),
  createdByIdx: index("system_backups_created_by_idx").on(table.createdBy),
}));

// Students table
export const students = pgTable("students", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  rollNumber: varchar("roll_number", { length: 50 }).notNull().unique(),
  programId: uuid("program_id").references(() => academicPrograms.id).notNull(),
  batchId: uuid("batch_id").references(() => academicBatches.id).notNull(),
  sectionId: uuid("section_id").references(() => academicSections.id),
  streamId: uuid("stream_id").references(() => academicStreams.id), // For backward compatibility
  branchId: uuid("branch_id").references(() => academicBranches.id), // For specializations
  grade: varchar("grade", { length: 10 }).notNull(), // For backward compatibility
  section: varchar("section", { length: 5 }), // For backward compatibility
  currentSemester: semesterEnum("current_semester").default("1"),
  studentType: studentTypeEnum("student_type").notNull().default("regular"),
  isLateralEntry: boolean("is_lateral_entry").notNull().default(false),
  dateOfBirth: date("date_of_birth").notNull(),
  address: text("address"),
  parentName: varchar("parent_name", { length: 200 }),
  parentPhone: varchar("parent_phone", { length: 20 }),
  parentEmail: varchar("parent_email", { length: 255 }),
  admissionDate: date("admission_date").notNull(),
  bloodGroup: varchar("blood_group", { length: 5 }),
  emergencyContact: varchar("emergency_contact", { length: 20 }),
  medicalInfo: text("medical_info"),
  previousEducation: text("previous_education"), // For lateral entry students
  transferCertificate: varchar("transfer_certificate", { length: 255 }), // File path
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
  // Performance indexes for student queries
  userIdIdx: index("students_user_id_idx").on(table.userId),
  rollNumberIdx: index("students_roll_number_idx").on(table.rollNumber),
  programIdIdx: index("students_program_id_idx").on(table.programId),
  batchIdIdx: index("students_batch_id_idx").on(table.batchId),
  sectionIdIdx: index("students_section_id_idx").on(table.sectionId),
  statusIdx: index("students_status_idx").on(table.status),
  admissionDateIdx: index("students_admission_date_idx").on(table.admissionDate),
  // Composite indexes for common queries
  programBatchIdx: index("students_program_batch_idx").on(table.programId, table.batchId),
  batchSectionIdx: index("students_batch_section_idx").on(table.batchId, table.sectionId),
  statusProgramIdx: index("students_status_program_idx").on(table.status, table.programId),
}));

// Teachers table
export const teachers = pgTable("teachers", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  employeeId: varchar("employee_id", { length: 50 }).notNull().unique(),
  teacherType: teacherTypeEnum("teacher_type").notNull().default("permanent"),
  subjects: text("subjects"), // JSON array of subjects they can teach
  primarySubject: varchar("primary_subject", { length: 100 }).notNull(),
  qualification: text("qualification"),
  experience: integer("experience").notNull().default(0),
  joiningDate: date("joining_date").notNull(),
  contractEndDate: date("contract_end_date"), // For contract teachers
  salary: decimal("salary", { precision: 10, scale: 2 }),
  department: varchar("department", { length: 100 }),
  maxClassesPerWeek: integer("max_classes_per_week").default(20),
  currentClassLoad: integer("current_class_load").default(0),
  specialization: text("specialization"),
  certifications: text("certifications"), // JSON array
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Classes table - Represents specific class sections within a program/batch
export const classes = pgTable("classes", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(), // "Class 10-A Mathematics", "B.Tech CS Sem 1 - Section A"
  programId: uuid("program_id").references(() => academicPrograms.id).notNull(),
  batchId: uuid("batch_id").references(() => academicBatches.id).notNull(),
  grade: varchar("grade", { length: 10 }).notNull(), // For backward compatibility
  section: varchar("section", { length: 5 }).notNull(),
  subject: varchar("subject", { length: 100 }).notNull(),
  teacherId: uuid("teacher_id").references(() => teachers.id),
  assistantTeacherId: uuid("assistant_teacher_id").references(() => teachers.id), // For lab classes
  classType: varchar("class_type", { length: 20 }).default("theory"), // theory, practical, lab, tutorial
  room: varchar("room", { length: 50 }),
  capacity: integer("capacity").notNull().default(30),
  enrolledStudents: integer("enrolled_students").notNull().default(0),
  availableSeats: integer("available_seats").notNull().default(30),
  academicYear: varchar("academic_year", { length: 20 }).notNull(),
  semester: semesterEnum("semester").default("1"),
  credits: integer("credits").default(3), // For higher education
  isElective: boolean("is_elective").default(false),
  prerequisites: text("prerequisites"), // JSON array of prerequisite subjects
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Class Enrollments (Many-to-Many relationship between students and classes)
export const classEnrollments = pgTable("class_enrollments", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  classId: uuid("class_id").references(() => classes.id).notNull(),
  enrollmentDate: date("enrollment_date").notNull(),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Attendance table
export const attendance = pgTable("attendance", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  classId: uuid("class_id").references(() => classes.id).notNull(),
  date: date("date").notNull(),
  status: attendanceStatusEnum("status").notNull(),
  remarks: text("remarks"),
  markedBy: uuid("marked_by").references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Grades table
export const grades = pgTable("grades", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  classId: uuid("class_id").references(() => classes.id).notNull(),
  examType: varchar("exam_type", { length: 50 }).notNull(),
  subject: varchar("subject", { length: 100 }).notNull(),
  totalMarks: integer("total_marks").notNull(),
  obtainedMarks: integer("obtained_marks").notNull(),
  grade: gradeEnum("grade"),
  percentage: decimal("percentage", { precision: 5, scale: 2 }),
  examDate: date("exam_date"),
  gradedBy: uuid("graded_by").references(() => teachers.id),
  remarks: text("remarks"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Assignments table
export const assignments = pgTable("assignments", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: varchar("title", { length: 200 }).notNull(),
  description: text("description"),
  classId: uuid("class_id").references(() => classes.id).notNull(),
  teacherId: uuid("teacher_id").references(() => teachers.id).notNull(),
  subject: varchar("subject", { length: 100 }).notNull(),
  totalMarks: integer("total_marks").notNull(),
  dueDate: timestamp("due_date").notNull(),
  assignedDate: timestamp("assigned_date").notNull().defaultNow(),
  instructions: text("instructions"),
  attachments: text("attachments"), // JSON array of file URLs
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Assignment Submissions table
export const assignmentSubmissions = pgTable("assignment_submissions", {
  id: uuid("id").primaryKey().defaultRandom(),
  assignmentId: uuid("assignment_id").references(() => assignments.id).notNull(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  submissionText: text("submission_text"),
  attachments: text("attachments"), // JSON array of file URLs
  submittedAt: timestamp("submitted_at").notNull().defaultNow(),
  obtainedMarks: integer("obtained_marks"),
  feedback: text("feedback"),
  gradedBy: uuid("graded_by").references(() => teachers.id),
  gradedAt: timestamp("graded_at"),
  status: varchar("status", { length: 20 }).notNull().default("submitted"), // submitted, graded, late
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Fee Structures table
export const feeStructures = pgTable("fee_structures", {
  id: uuid("id").primaryKey().defaultRandom(),
  grade: varchar("grade", { length: 10 }).notNull(),
  academicYear: varchar("academic_year", { length: 20 }).notNull(),
  tuitionFee: decimal("tuition_fee", { precision: 10, scale: 2 }).notNull(),
  admissionFee: decimal("admission_fee", { precision: 10, scale: 2 }).default("0"),
  examFee: decimal("exam_fee", { precision: 10, scale: 2 }).default("0"),
  libraryFee: decimal("library_fee", { precision: 10, scale: 2 }).default("0"),
  transportFee: decimal("transport_fee", { precision: 10, scale: 2 }).default("0"),
  hostelFee: decimal("hostel_fee", { precision: 10, scale: 2 }).default("0"),
  miscellaneousFee: decimal("miscellaneous_fee", { precision: 10, scale: 2 }).default("0"),
  totalFee: decimal("total_fee", { precision: 10, scale: 2 }).notNull(),
  dueDate: date("due_date"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Fee Payments table
export const feePayments = pgTable("fee_payments", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  feeStructureId: uuid("fee_structure_id").references(() => feeStructures.id).notNull(),
  receiptNumber: varchar("receipt_number", { length: 50 }).notNull().unique(),
  amountPaid: decimal("amount_paid", { precision: 10, scale: 2 }).notNull(),
  paymentMethod: paymentMethodEnum("payment_method").notNull(),
  paymentDate: date("payment_date").notNull(),
  transactionId: varchar("transaction_id", { length: 100 }),
  remarks: text("remarks"),
  status: paymentStatusEnum("status").notNull().default("completed"),
  processedBy: uuid("processed_by").references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Books table (Library Management)
export const books = pgTable("books", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: varchar("title", { length: 300 }).notNull(),
  author: varchar("author", { length: 200 }).notNull(),
  isbn: varchar("isbn", { length: 20 }).unique(),
  category: varchar("category", { length: 100 }).notNull(),
  publisher: varchar("publisher", { length: 200 }),
  publicationYear: integer("publication_year"),
  totalCopies: integer("total_copies").notNull().default(1),
  availableCopies: integer("available_copies").notNull().default(1),
  location: varchar("location", { length: 100 }), // Shelf location
  description: text("description"),
  price: decimal("price", { precision: 8, scale: 2 }),
  status: bookStatusEnum("status").notNull().default("available"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Book Issues table
export const bookIssues = pgTable("book_issues", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookId: uuid("book_id").references(() => books.id).notNull(),
  studentId: uuid("student_id").references(() => students.id),
  teacherId: uuid("teacher_id").references(() => teachers.id),
  issueDate: date("issue_date").notNull(),
  dueDate: date("due_date").notNull(),
  returnDate: date("return_date"),
  fineAmount: decimal("fine_amount", { precision: 8, scale: 2 }).default("0"),
  status: issueStatusEnum("status").notNull().default("issued"),
  issuedBy: uuid("issued_by").references(() => users.id),
  returnedBy: uuid("returned_by").references(() => users.id),
  remarks: text("remarks"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Transport Routes table
export const transportRoutes = pgTable("transport_routes", {
  id: uuid("id").primaryKey().defaultRandom(),
  routeName: varchar("route_name", { length: 100 }).notNull(),
  routeCode: varchar("route_code", { length: 20 }).unique(),
  startPoint: varchar("start_point", { length: 200 }).notNull(),
  endPoint: varchar("end_point", { length: 200 }).notNull(),
  stops: text("stops"), // JSON array of stops
  distance: decimal("distance", { precision: 6, scale: 2 }), // in kilometers
  estimatedTime: integer("estimated_time"), // in minutes
  fee: decimal("fee", { precision: 8, scale: 2 }).notNull(),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Vehicles table
export const vehicles = pgTable("vehicles", {
  id: uuid("id").primaryKey().defaultRandom(),
  vehicleNumber: varchar("vehicle_number", { length: 20 }).notNull().unique(),
  vehicleType: vehicleTypeEnum("vehicle_type").notNull(),
  capacity: integer("capacity").notNull(),
  routeId: uuid("route_id").references(() => transportRoutes.id),
  driverName: varchar("driver_name", { length: 100 }).notNull(),
  driverPhone: varchar("driver_phone", { length: 20 }).notNull(),
  driverLicense: varchar("driver_license", { length: 50 }),
  insuranceNumber: varchar("insurance_number", { length: 50 }),
  insuranceExpiry: date("insurance_expiry"),
  permitNumber: varchar("permit_number", { length: 50 }),
  permitExpiry: date("permit_expiry"),
  lastMaintenance: date("last_maintenance"),
  nextMaintenance: date("next_maintenance"),
  fuelType: varchar("fuel_type", { length: 20 }),
  status: vehicleStatusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Hostels table
export const hostels = pgTable("hostels", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(),
  type: hostelTypeEnum("type").notNull(),
  address: text("address").notNull(),
  wardenName: varchar("warden_name", { length: 100 }).notNull(),
  wardenPhone: varchar("warden_phone", { length: 20 }).notNull(),
  wardenEmail: varchar("warden_email", { length: 255 }),
  totalRooms: integer("total_rooms").notNull().default(0),
  totalBeds: integer("total_beds").notNull().default(0),
  facilities: text("facilities"), // JSON array of facilities
  monthlyFee: decimal("monthly_fee", { precision: 8, scale: 2 }).notNull(),
  securityDeposit: decimal("security_deposit", { precision: 8, scale: 2 }).default("0"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Hostel Rooms table
export const hostelRooms = pgTable("hostel_rooms", {
  id: uuid("id").primaryKey().defaultRandom(),
  hostelId: uuid("hostel_id").references(() => hostels.id).notNull(),
  roomNumber: varchar("room_number", { length: 20 }).notNull(),
  roomType: roomTypeEnum("room_type").notNull(),
  floor: integer("floor"),
  capacity: integer("capacity").notNull(),
  occupiedBeds: integer("occupied_beds").notNull().default(0),
  monthlyRent: decimal("monthly_rent", { precision: 8, scale: 2 }).notNull(),
  facilities: text("facilities"), // JSON array of room facilities
  status: roomStatusEnum("status").notNull().default("available"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Hostel Allocations table
export const hostelAllocations = pgTable("hostel_allocations", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  roomId: uuid("room_id").references(() => hostelRooms.id).notNull(),
  allocationDate: date("allocation_date").notNull(),
  vacationDate: date("vacation_date"),
  monthlyFee: decimal("monthly_fee", { precision: 8, scale: 2 }).notNull(),
  securityDeposit: decimal("security_deposit", { precision: 8, scale: 2 }).default("0"),
  status: statusEnum("status").notNull().default("active"),
  allocatedBy: uuid("allocated_by").references(() => users.id),
  remarks: text("remarks"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Exams table
export const exams = pgTable("exams", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 200 }).notNull(),
  type: examTypeEnum("type").notNull(),
  grade: varchar("grade", { length: 10 }).notNull(),
  subject: varchar("subject", { length: 100 }).notNull(),
  date: date("date").notNull(),
  startTime: time("start_time").notNull(),
  endTime: time("end_time").notNull(),
  totalMarks: integer("total_marks").notNull(),
  passingMarks: integer("passing_marks").notNull(),
  room: varchar("room", { length: 50 }),
  invigilator: varchar("invigilator", { length: 100 }),
  instructions: text("instructions"),
  academicYear: varchar("academic_year", { length: 20 }).notNull(),
  semester: varchar("semester", { length: 20 }),
  status: examStatusEnum("status").notNull().default("scheduled"),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Timetables table
export const timetables = pgTable("timetables", {
  id: uuid("id").primaryKey().defaultRandom(),
  classId: uuid("class_id").references(() => classes.id).notNull(),
  teacherId: uuid("teacher_id").references(() => teachers.id).notNull(),
  subject: varchar("subject", { length: 100 }).notNull(),
  day: dayEnum("day").notNull(),
  startTime: time("start_time").notNull(),
  endTime: time("end_time").notNull(),
  room: varchar("room", { length: 50 }),
  academicYear: varchar("academic_year", { length: 20 }).notNull(),
  semester: varchar("semester", { length: 20 }),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Student Transport table (Many-to-Many relationship)
export const studentTransport = pgTable("student_transport", {
  id: uuid("id").primaryKey().defaultRandom(),
  studentId: uuid("student_id").references(() => students.id).notNull(),
  routeId: uuid("route_id").references(() => transportRoutes.id).notNull(),
  pickupPoint: varchar("pickup_point", { length: 200 }),
  monthlyFee: decimal("monthly_fee", { precision: 8, scale: 2 }).notNull(),
  startDate: date("start_date").notNull(),
  endDate: date("end_date"),
  status: statusEnum("status").notNull().default("active"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Relations

// Academic Programs Relations
export const academicProgramsRelations = relations(academicPrograms, ({ one, many }) => ({
  stream: one(academicStreams, {
    fields: [academicPrograms.streamId],
    references: [academicStreams.id],
  }),
  branch: one(academicBranches, {
    fields: [academicPrograms.branchId],
    references: [academicBranches.id],
  }),
  batches: many(academicBatches),
  students: many(students),
  classes: many(classes),
  sections: many(academicSections),
}));

// Academic Batches Relations
export const academicBatchesRelations = relations(academicBatches, ({ one, many }) => ({
  program: one(academicPrograms, {
    fields: [academicBatches.programId],
    references: [academicPrograms.id],
  }),
  students: many(students),
  classes: many(classes),
}));

export const usersRelations = relations(users, ({ one, many }) => ({
  student: one(students, {
    fields: [users.id],
    references: [students.userId],
  }),
  teacher: one(teachers, {
    fields: [users.id],
    references: [teachers.userId],
  }),
  attendanceMarked: many(attendance),
  feePaymentsProcessed: many(feePayments),
  bookIssuesProcessed: many(bookIssues),
  hostelAllocations: many(hostelAllocations),
  examsCreated: many(exams),
}));

export const studentsRelations = relations(students, ({ one, many }) => ({
  user: one(users, {
    fields: [students.userId],
    references: [users.id],
  }),
  program: one(academicPrograms, {
    fields: [students.programId],
    references: [academicPrograms.id],
  }),
  batch: one(academicBatches, {
    fields: [students.batchId],
    references: [academicBatches.id],
  }),
  section: one(academicSections, {
    fields: [students.sectionId],
    references: [academicSections.id],
  }),
  stream: one(academicStreams, {
    fields: [students.streamId],
    references: [academicStreams.id],
  }),
  branch: one(academicBranches, {
    fields: [students.branchId],
    references: [academicBranches.id],
  }),
  classEnrollments: many(classEnrollments),
  attendance: many(attendance),
  grades: many(grades),
  assignmentSubmissions: many(assignmentSubmissions),
  feePayments: many(feePayments),
  bookIssues: many(bookIssues),
  hostelAllocations: many(hostelAllocations),
  studentTransport: many(studentTransport),
}));

export const teachersRelations = relations(teachers, ({ one, many }) => ({
  user: one(users, {
    fields: [teachers.userId],
    references: [users.id],
  }),
  classes: many(classes),
  assignments: many(assignments),
  assignmentSubmissions: many(assignmentSubmissions),
  grades: many(grades),
  bookIssues: many(bookIssues),
  timetables: many(timetables),
}));

export const classesRelations = relations(classes, ({ one, many }) => ({
  program: one(academicPrograms, {
    fields: [classes.programId],
    references: [academicPrograms.id],
  }),
  batch: one(academicBatches, {
    fields: [classes.batchId],
    references: [academicBatches.id],
  }),
  teacher: one(teachers, {
    fields: [classes.teacherId],
    references: [teachers.id],
  }),
  assistantTeacher: one(teachers, {
    fields: [classes.assistantTeacherId],
    references: [teachers.id],
  }),
  enrollments: many(classEnrollments),
  attendance: many(attendance),
  grades: many(grades),
  assignments: many(assignments),
  timetables: many(timetables),
}));

export const classEnrollmentsRelations = relations(classEnrollments, ({ one }) => ({
  student: one(students, {
    fields: [classEnrollments.studentId],
    references: [students.id],
  }),
  class: one(classes, {
    fields: [classEnrollments.classId],
    references: [classes.id],
  }),
}));

export const attendanceRelations = relations(attendance, ({ one }) => ({
  student: one(students, {
    fields: [attendance.studentId],
    references: [students.id],
  }),
  class: one(classes, {
    fields: [attendance.classId],
    references: [classes.id],
  }),
  markedBy: one(users, {
    fields: [attendance.markedBy],
    references: [users.id],
  }),
}));

export const gradesRelations = relations(grades, ({ one }) => ({
  student: one(students, {
    fields: [grades.studentId],
    references: [students.id],
  }),
  class: one(classes, {
    fields: [grades.classId],
    references: [classes.id],
  }),
  gradedBy: one(teachers, {
    fields: [grades.gradedBy],
    references: [teachers.id],
  }),
}));

export const assignmentsRelations = relations(assignments, ({ one, many }) => ({
  class: one(classes, {
    fields: [assignments.classId],
    references: [classes.id],
  }),
  teacher: one(teachers, {
    fields: [assignments.teacherId],
    references: [teachers.id],
  }),
  submissions: many(assignmentSubmissions),
}));

export const assignmentSubmissionsRelations = relations(assignmentSubmissions, ({ one }) => ({
  assignment: one(assignments, {
    fields: [assignmentSubmissions.assignmentId],
    references: [assignments.id],
  }),
  student: one(students, {
    fields: [assignmentSubmissions.studentId],
    references: [students.id],
  }),
  gradedBy: one(teachers, {
    fields: [assignmentSubmissions.gradedBy],
    references: [teachers.id],
  }),
}));

export const feeStructuresRelations = relations(feeStructures, ({ many }) => ({
  payments: many(feePayments),
}));

export const feePaymentsRelations = relations(feePayments, ({ one }) => ({
  student: one(students, {
    fields: [feePayments.studentId],
    references: [students.id],
  }),
  feeStructure: one(feeStructures, {
    fields: [feePayments.feeStructureId],
    references: [feeStructures.id],
  }),
  processedBy: one(users, {
    fields: [feePayments.processedBy],
    references: [users.id],
  }),
}));

export const booksRelations = relations(books, ({ many }) => ({
  issues: many(bookIssues),
}));

export const bookIssuesRelations = relations(bookIssues, ({ one }) => ({
  book: one(books, {
    fields: [bookIssues.bookId],
    references: [books.id],
  }),
  student: one(students, {
    fields: [bookIssues.studentId],
    references: [students.id],
  }),
  teacher: one(teachers, {
    fields: [bookIssues.teacherId],
    references: [teachers.id],
  }),
  issuedBy: one(users, {
    fields: [bookIssues.issuedBy],
    references: [users.id],
  }),
  returnedBy: one(users, {
    fields: [bookIssues.returnedBy],
    references: [users.id],
  }),
}));

export const transportRoutesRelations = relations(transportRoutes, ({ many }) => ({
  vehicles: many(vehicles),
  studentTransport: many(studentTransport),
}));

export const vehiclesRelations = relations(vehicles, ({ one }) => ({
  route: one(transportRoutes, {
    fields: [vehicles.routeId],
    references: [transportRoutes.id],
  }),
}));

export const hostelsRelations = relations(hostels, ({ many }) => ({
  rooms: many(hostelRooms),
}));

export const hostelRoomsRelations = relations(hostelRooms, ({ one, many }) => ({
  hostel: one(hostels, {
    fields: [hostelRooms.hostelId],
    references: [hostels.id],
  }),
  allocations: many(hostelAllocations),
}));

export const hostelAllocationsRelations = relations(hostelAllocations, ({ one }) => ({
  student: one(students, {
    fields: [hostelAllocations.studentId],
    references: [students.id],
  }),
  room: one(hostelRooms, {
    fields: [hostelAllocations.roomId],
    references: [hostelRooms.id],
  }),
  allocatedBy: one(users, {
    fields: [hostelAllocations.allocatedBy],
    references: [users.id],
  }),
}));

export const examsRelations = relations(exams, ({ one }) => ({
  createdBy: one(users, {
    fields: [exams.createdBy],
    references: [users.id],
  }),
}));

export const timetablesRelations = relations(timetables, ({ one }) => ({
  class: one(classes, {
    fields: [timetables.classId],
    references: [classes.id],
  }),
  teacher: one(teachers, {
    fields: [timetables.teacherId],
    references: [teachers.id],
  }),
}));

export const studentTransportRelations = relations(studentTransport, ({ one }) => ({
  student: one(students, {
    fields: [studentTransport.studentId],
    references: [students.id],
  }),
  route: one(transportRoutes, {
    fields: [studentTransport.routeId],
    references: [transportRoutes.id],
  }),
}));

// New Relations for Enhanced Tables

// Institution Config Relations
export const institutionConfigRelations = relations(institutionConfig, ({ one }) => ({
  configuredBy: one(users, {
    fields: [institutionConfig.configuredBy],
    references: [users.id],
  }),
}));

// Academic Streams Relations
export const academicStreamsRelations = relations(academicStreams, ({ many }) => ({
  branches: many(academicBranches),
  programs: many(academicPrograms),
  sections: many(academicSections),
  students: many(students),
}));

// Academic Branches Relations
export const academicBranchesRelations = relations(academicBranches, ({ one, many }) => ({
  stream: one(academicStreams, {
    fields: [academicBranches.streamId],
    references: [academicStreams.id],
  }),
  programs: many(academicPrograms),
  sections: many(academicSections),
  students: many(students),
}));

// Academic Sections Relations
export const academicSectionsRelations = relations(academicSections, ({ one, many }) => ({
  program: one(academicPrograms, {
    fields: [academicSections.programId],
    references: [academicPrograms.id],
  }),
  batch: one(academicBatches, {
    fields: [academicSections.batchId],
    references: [academicBatches.id],
  }),
  stream: one(academicStreams, {
    fields: [academicSections.streamId],
    references: [academicStreams.id],
  }),
  branch: one(academicBranches, {
    fields: [academicSections.branchId],
    references: [academicBranches.id],
  }),
  classTeacher: one(teachers, {
    fields: [academicSections.classTeacherId],
    references: [teachers.id],
  }),
  students: many(students),
}));