import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { academicBatches, academicPrograms, students, classes } from "@/lib/db/schema";
import { eq, count, and } from "drizzle-orm";

// Validation schemas
const academicBatchSchema = z.object({
  programId: z.string().min(1, "Program ID is required"),
  batchName: z.string().min(2, "Batch name must be at least 2 characters"),
  startYear: z.number().min(2020, "Start year must be valid"),
  endYear: z.number().min(2020, "End year must be valid"),
  currentSemester: z.enum(["1", "2", "3", "4", "5", "6", "7", "8"]).optional(),
  totalSeats: z.number().min(1, "Total seats must be at least 1"),
  admissionStatus: z.enum(["open", "closed", "waitlist"]).optional(),
}).refine((data) => data.endYear > data.startYear, {
  message: "End year must be after start year",
  path: ["endYear"],
});

const updateAcademicBatchSchema = z.object({
  programId: z.string().min(1, "Program ID is required").optional(),
  batchName: z.string().min(2, "Batch name must be at least 2 characters").optional(),
  startYear: z.number().min(2020, "Start year must be valid").optional(),
  endYear: z.number().min(2020, "End year must be valid").optional(),
  currentSemester: z.enum(["1", "2", "3", "4", "5", "6", "7", "8"]).optional(),
  totalSeats: z.number().min(1, "Total seats must be at least 1").optional(),
  availableSeats: z.number().min(0).optional(),
  occupiedSeats: z.number().min(0).optional(),
  admissionStatus: z.enum(["open", "closed", "waitlist"]).optional(),
});

const app = new Hono()
  // Get all academic batches with statistics
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "10");
      const programId = c.req.query("programId");
      const status = c.req.query("status");
      const admissionStatus = c.req.query("admissionStatus");

      // Build query conditions
      let whereConditions = [];

      if (programId) {
        whereConditions.push(eq(academicBatches.programId, programId));
      }

      if (status) {
        whereConditions.push(eq(academicBatches.status, status as any));
      }

      if (admissionStatus) {
        whereConditions.push(eq(academicBatches.admissionStatus, admissionStatus as any));
      }

      // Get batches with program info
      const batches = await db
        .select({
          id: academicBatches.id,
          programId: academicBatches.programId,
          batchName: academicBatches.batchName,
          startYear: academicBatches.startYear,
          endYear: academicBatches.endYear,
          currentSemester: academicBatches.currentSemester,
          totalSeats: academicBatches.totalSeats,
          occupiedSeats: academicBatches.occupiedSeats,
          availableSeats: academicBatches.availableSeats,
          admissionStatus: academicBatches.admissionStatus,
          status: academicBatches.status,
          createdAt: academicBatches.createdAt,
          updatedAt: academicBatches.updatedAt,
          programName: academicPrograms.name,
          programCode: academicPrograms.code,
          programType: academicPrograms.type,
          department: academicPrograms.department,
        })
        .from(academicBatches)
        .leftJoin(academicPrograms, eq(academicBatches.programId, academicPrograms.id))
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

      // Get statistics for each batch
      const batchesWithStats = await Promise.all(
        batches.map(async (batch) => {
          // Get enrolled students count
          const [studentCount] = await db
            .select({ count: count() })
            .from(students)
            .where(eq(students.batchId, batch.id));

          // Get classes count
          const [classCount] = await db
            .select({ count: count() })
            .from(classes)
            .where(eq(classes.batchId, batch.id));

          // Get student types breakdown
          const regularStudents = await db
            .select({ count: count() })
            .from(students)
            .where(
              and(
                eq(students.batchId, batch.id),
                eq(students.studentType, "regular")
              )
            );

          const lateralStudents = await db
            .select({ count: count() })
            .from(students)
            .where(
              and(
                eq(students.batchId, batch.id),
                eq(students.studentType, "lateral")
              )
            );

          const distanceStudents = await db
            .select({ count: count() })
            .from(students)
            .where(
              and(
                eq(students.batchId, batch.id),
                eq(students.studentType, "distance")
              )
            );

          return {
            ...batch,
            statistics: {
              totalStudents: studentCount.count,
              totalClasses: classCount.count,
              regularStudents: regularStudents[0].count,
              lateralStudents: lateralStudents[0].count,
              distanceStudents: distanceStudents[0].count,
              occupancyRate: batch.totalSeats > 0 ?
                Math.round((studentCount.count / batch.totalSeats) * 100) : 0,
            },
          };
        })
      );

      // Pagination
      const offset = (page - 1) * limit;
      const paginatedBatches = batchesWithStats.slice(offset, offset + limit);

      const meta = {
        page,
        limit,
        total: batchesWithStats.length,
        totalPages: Math.ceil(batchesWithStats.length / limit),
        hasNext: page < Math.ceil(batchesWithStats.length / limit),
        hasPrev: page > 1,
      };

      return c.json({ data: paginatedBatches, meta });
    } catch (error) {
      console.error("Error fetching academic batches:", error);
      return c.json({ error: "Failed to fetch academic batches" }, 500);
    }
  })

  // Get single academic batch with detailed information
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [batch] = await db
        .select({
          id: academicBatches.id,
          programId: academicBatches.programId,
          batchName: academicBatches.batchName,
          startYear: academicBatches.startYear,
          endYear: academicBatches.endYear,
          currentSemester: academicBatches.currentSemester,
          totalSeats: academicBatches.totalSeats,
          occupiedSeats: academicBatches.occupiedSeats,
          availableSeats: academicBatches.availableSeats,
          admissionStatus: academicBatches.admissionStatus,
          status: academicBatches.status,
          createdAt: academicBatches.createdAt,
          updatedAt: academicBatches.updatedAt,
          programName: academicPrograms.name,
          programCode: academicPrograms.code,
          programType: academicPrograms.type,
          department: academicPrograms.department,
        })
        .from(academicBatches)
        .leftJoin(academicPrograms, eq(academicBatches.programId, academicPrograms.id))
        .where(eq(academicBatches.id, id));

      if (!batch) {
        return c.json({ error: "Academic batch not found" }, 404);
      }

      // Get students in this batch
      const batchStudents = await db
        .select()
        .from(students)
        .where(eq(students.batchId, id));

      // Get classes in this batch
      const batchClasses = await db
        .select()
        .from(classes)
        .where(eq(classes.batchId, id));

      return c.json({
        data: {
          ...batch,
          students: batchStudents,
          classes: batchClasses,
        },
      });
    } catch (error) {
      console.error("Error fetching academic batch:", error);
      return c.json({ error: "Failed to fetch academic batch" }, 500);
    }
  })

  // Create new academic batch
  .post("/", zValidator("json", academicBatchSchema), async (c) => {
    try {
      const values = c.req.valid("json");

      // Verify program exists
      const [program] = await db
        .select()
        .from(academicPrograms)
        .where(eq(academicPrograms.id, values.programId));

      if (!program) {
        return c.json({ error: "Academic program not found" }, 404);
      }

      // Calculate available seats (initially same as total seats)
      const batchData = {
        ...values,
        availableSeats: values.totalSeats,
        occupiedSeats: 0,
      };

      const [newBatch] = await db
        .insert(academicBatches)
        .values(batchData)
        .returning();

      return c.json({ data: newBatch }, 201);
    } catch (error) {
      console.error("Error creating academic batch:", error);
      return c.json({ error: "Failed to create academic batch" }, 500);
    }
  })

  // Update academic batch
  .put("/:id", zValidator("json", updateAcademicBatchSchema), async (c) => {
    try {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      // If totalSeats is being updated, recalculate availableSeats
      if (values.totalSeats) {
        const [currentBatch] = await db
          .select()
          .from(academicBatches)
          .where(eq(academicBatches.id, id));

        if (currentBatch) {
          values.availableSeats = values.totalSeats - currentBatch.occupiedSeats;
        }
      }

      const [updatedBatch] = await db
        .update(academicBatches)
        .set({ ...values, updatedAt: new Date() })
        .where(eq(academicBatches.id, id))
        .returning();

      if (!updatedBatch) {
        return c.json({ error: "Academic batch not found" }, 404);
      }

      return c.json({ data: updatedBatch });
    } catch (error) {
      console.error("Error updating academic batch:", error);
      return c.json({ error: "Failed to update academic batch" }, 500);
    }
  })

  // Delete academic batch
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Check if batch has students
      const [studentCount] = await db
        .select({ count: count() })
        .from(students)
        .where(eq(students.batchId, id));

      if (studentCount.count > 0) {
        return c.json({
          error: "Cannot delete batch with existing students"
        }, 400);
      }

      const [deletedBatch] = await db
        .delete(academicBatches)
        .where(eq(academicBatches.id, id))
        .returning();

      if (!deletedBatch) {
        return c.json({ error: "Academic batch not found" }, 404);
      }

      return c.json({ message: "Academic batch deleted successfully" });
    } catch (error) {
      console.error("Error deleting academic batch:", error);
      return c.json({ error: "Failed to delete academic batch" }, 500);
    }
  })

  // Get batch students with sections
  .get("/:id/students", async (c) => {
    try {
      const id = c.req.param("id");
      const section = c.req.query("section");

      const whereConditions = [eq(students.batchId, id)];

      if (section) {
        whereConditions.push(eq(students.section, section));
      }

      const batchStudents = await db
        .select()
        .from(students)
        .where(and(...whereConditions));

      // Group students by section
      const studentsBySection = batchStudents.reduce((acc, student) => {
        const sectionKey = student.section || "No Section";
        if (!acc[sectionKey]) {
          acc[sectionKey] = [];
        }
        acc[sectionKey].push(student);
        return acc;
      }, {} as Record<string, typeof batchStudents>);

      return c.json({
        data: {
          students: batchStudents,
          sections: studentsBySection,
        }
      });
    } catch (error) {
      console.error("Error fetching batch students:", error);
      return c.json({ error: "Failed to fetch batch students" }, 500);
    }
  });

export default app;
