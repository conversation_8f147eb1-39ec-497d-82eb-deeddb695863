"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  UserPlus,
  GraduationCap,
  AlertCircle,
  CheckCircle,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Users,
  BookOpen,
  Plus,
  Search,
  UserCheck,
  CreditCard
} from "lucide-react";
import { useCreateStudent } from "@/features/api/use-students";
import { toast } from "sonner";
// Removed hierarchical program selector - using simple class selection for school-only mode

export default function NewStudentPage() {
  const [user, setUser] = useState<any>(null);
  const [formData, setFormData] = useState({
    // Student Information
    firstName: "",
    lastName: "",
    middleName: "",
    email: "",
    phone: "",
    dateOfBirth: "",
    gender: "",
    nationality: "Indian",
    religion: "",
    category: "", // General, OBC, SC, ST, etc.
    caste: "",
    motherTongue: "",
    address: "",
    permanentAddress: "",
    city: "",
    state: "",
    pincode: "",
    country: "India",
    rollNumber: "",
    bloodGroup: "",
    emergencyContact: "",
    medicalInfo: "",
    disabilities: "",
    previousSchool: "",
    previousClass: "",
    tcNumber: "", // Transfer Certificate Number
    tcDate: "",
    migrationCertificate: "",

    // Academic Information (School-specific)
    grade: "", // Class/Grade (Nursery to Grade 12)
    program: "", // Program name for display (school programs only)
    programId: "", // Program ID for database
    batchId: "", // Selected batch ID
    stream: "", // For grades 11-12 (Science, Commerce, Arts)
    admissionType: "",
    studentType: "regular",
    admissionDate: new Date().toISOString().split('T')[0],
    academicYear: "",

    // Financial Information
    feeCategory: "regular", // regular, scholarship, concession
    scholarshipType: "",
    scholarshipAmount: "",
    transportRequired: false,
    hostelRequired: false,

    // Parent/Guardian Information
    useExistingParent: false,
    existingParentId: "",

    // Father Information
    fatherFirstName: "",
    fatherLastName: "",
    fatherEmail: "",
    fatherPhone: "",
    fatherOccupation: "",
    fatherIncome: "",
    fatherEducation: "",
    fatherAadhar: "",

    // Mother Information
    motherFirstName: "",
    motherLastName: "",
    motherEmail: "",
    motherPhone: "",
    motherOccupation: "",
    motherIncome: "",
    motherEducation: "",
    motherAadhar: "",

    // Guardian Information (if different from parents)
    guardianRequired: false,
    guardianFirstName: "",
    guardianLastName: "",
    guardianEmail: "",
    guardianPhone: "",
    guardianRelation: "",
    guardianAddress: "",
    guardianAadhar: "",

    // Document Information
    studentAadhar: "",
    birthCertificate: "",
    casteCertificate: "",
    incomeCertificate: "",
    domicileCertificate: "",

    // Legacy fields for backward compatibility
    parentFirstName: "",
    parentLastName: "",
    parentEmail: "",
    parentPhone: "",
    parentAddress: "",
    relationship: "father",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [institutionConfig, setInstitutionConfig] = useState<any>(null);
  const [admissionOptions, setAdmissionOptions] = useState<any>(null);
  const [selectedFeeStructure, setSelectedFeeStructure] = useState<any>(null);
  const [availableParents, setAvailableParents] = useState([]);
  const [availableBatches, setAvailableBatches] = useState([]);
  const [availableStreams, setAvailableStreams] = useState([]);
  const [availableBranches, setAvailableBranches] = useState([]);
  const [sectionAssignment, setSectionAssignment] = useState<any>(null);
  const [showParentSearch, setShowParentSearch] = useState(false);

  const router = useRouter();
  const createStudentMutation = useCreateStudent();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin", "admission_officer"].includes(parsedUser.role)) {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadInitialData();
  }, [router]);

  const loadInitialData = async () => {
    try {
      // Load institution configuration
      const configResponse = await fetch("/api/institution-config");
      if (configResponse.ok) {
        const configData = await configResponse.json();
        setInstitutionConfig(configData.data);
      }

      // Load admission options (institute-specific)
      const optionsResponse = await fetch("/api/fee-structures/admission-options");
      if (optionsResponse.ok) {
        const optionsData = await optionsResponse.json();
        setAdmissionOptions(optionsData.data);
      }

      // Load existing parents
      const parentsResponse = await fetch("/api/users?role=parent&limit=1000");
      if (parentsResponse.ok) {
        const parentsData = await parentsResponse.json();
        setAvailableParents(parentsData.data || []);
      }
    } catch (error) {
      console.error("Error loading initial data:", error);
      toast.error("Failed to load admission options. Please check institution configuration.");
    }
  };

  const loadBatches = async (programId: string) => {
    try {
      const response = await fetch(`/api/academic-batches?programId=${programId}`);
      if (response.ok) {
        const data = await response.json();
        setAvailableBatches(data.data || []);
      }
    } catch (error) {
      console.error("Error loading batches:", error);
    }
  };

  const loadStreams = async (programId: string) => {
    try {
      const response = await fetch(`/api/academic-streams?programId=${programId}`);
      if (response.ok) {
        const data = await response.json();
        setAvailableStreams(data.data || []);
      }
    } catch (error) {
      console.error("Error loading streams:", error);
    }
  };

  const loadFeeStructure = async (gradeOrProgram: string) => {
    if (!admissionOptions?.currentAcademicYear) return;

    try {
      const response = await fetch(`/api/fee-structures/by-grade/${gradeOrProgram}/${admissionOptions.currentAcademicYear}`);
      if (response.ok) {
        const data = await response.json();
        setSelectedFeeStructure(data.data);
      } else {
        setSelectedFeeStructure(null);
        toast.info(`No fee structure found for ${gradeOrProgram}. Please contact administration.`);
      }
    } catch (error) {
      console.error("Error loading fee structure:", error);
      setSelectedFeeStructure(null);
    }
  };

  const checkSectionAvailability = async (programId: string, batchId: string, streamId?: string) => {
    try {
      const params = new URLSearchParams({ programId, batchId });
      if (streamId) params.append("streamId", streamId);

      const response = await fetch(`/api/academic-sections/available?${params}`);
      if (response.ok) {
        const data = await response.json();
        setSectionAssignment(data.data);
        return data.data;
      }
    } catch (error) {
      console.error("Error checking section availability:", error);
    }
    return null;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Handle institute-specific logic
    if (field === "grade" && admissionOptions?.institutionType === "school") {
      // Load fee structure for selected grade
      if (value) {
        loadFeeStructure(value as string);
      } else {
        setSelectedFeeStructure(null);
      }
    }

    if (field === "programId") {
      // When program is selected, load batches and fee structure (for both school and college)
      if (value) {
        const selectedProgram = admissionOptions.programs.find((p: any) => p.id === value);
        if (selectedProgram) {
          setFormData(prev => ({
            ...prev,
            program: selectedProgram.name,
            programId: selectedProgram.id,
            batchId: "", // Reset dependent fields
            streamId: "",
            branchId: "",
          }));
          loadBatches(value as string);
          loadFeeStructure(selectedProgram.name);
        }
      } else {
        setFormData(prev => ({
          ...prev,
          program: "",
          programId: "",
          batchId: "",
          streamId: "",
          branchId: "",
        }));
        setAvailableBatches([]);
        setSelectedFeeStructure(null);
      }
    }

    if (field === "batchId" && value) {
      // When batch is selected, check section availability
      if (formData.programId) {
        checkSectionAvailability(formData.programId, value as string, formData.stream);
      }
    }
  };

  // Handler for hierarchical program selection
  const handleProgramSelection = (selection: any) => {
    if (selection) {
      setFormData(prev => ({
        ...prev,
        programId: selection.programId,
        program: selection.programName,
        batchId: "", // Reset batch selection
      }));

      // Load batches for the selected program
      loadBatches(selection.programId);

      // Load fee structure
      loadFeeStructure(selection.programName);

      // Reset section assignment
      setSectionAssignment(null);
    } else {
      // Clear selection
      setFormData(prev => ({
        ...prev,
        programId: "",
        program: "",
        batchId: "",
      }));
      setAvailableBatches([]);
      setSelectedFeeStructure(null);
      setSectionAssignment(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate section availability
      if (!sectionAssignment) {
        toast.error("Please select program and batch to check section availability");
        return;
      }

      if (sectionAssignment.availableSeats <= 0) {
        toast.error("No seats available in any section for this program/batch");
        return;
      }

      // Prepare student data
      const studentData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        rollNumber: formData.rollNumber,
        programId: formData.programId,
        batchId: formData.batchId,
        grade: sectionAssignment.grade || "1", // From section assignment
        section: sectionAssignment.assignedSection, // Auto-assigned section
        sectionId: sectionAssignment.sectionId, // Section ID for database
        studentType: formData.studentType,
        dateOfBirth: formData.dateOfBirth,
        address: formData.address,
        bloodGroup: formData.bloodGroup,
        emergencyContact: formData.emergencyContact,
        medicalInfo: formData.medicalInfo,
        admissionDate: formData.admissionDate,

        // Parent information
        useExistingParent: formData.useExistingParent,
        existingParentId: formData.existingParentId,
        parentFirstName: formData.parentFirstName,
        parentLastName: formData.parentLastName,
        parentEmail: formData.parentEmail,
        parentPhone: formData.parentPhone,
        parentAddress: formData.parentAddress,
        relationship: formData.relationship,
      };

      await createStudentMutation.mutateAsync(studentData);
      toast.success(`Student admitted successfully! Assigned to Section ${sectionAssignment.assignedSection}`);
      router.push("/admin/students");
    } catch (error) {
      console.error("Error creating student:", error);
      toast.error("Failed to create student");
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <GraduationCap className="h-6 w-6" />
                Add New Student
              </h1>
              <p className="text-gray-600">Register a new student in the school management system</p>
            </div>
          </div>
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <UserPlus className="h-3 w-3 mr-1" />
            Student Registration
          </Badge>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Student Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Student Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Basic Information</h4>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      placeholder="Enter first name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="middleName">Middle Name</Label>
                    <Input
                      id="middleName"
                      value={formData.middleName}
                      onChange={(e) => handleInputChange("middleName", e.target.value)}
                      placeholder="Enter middle name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      placeholder="Enter last name"
                      required
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        placeholder="Enter email address"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        placeholder="Enter phone number"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-4">
                  <div>
                    <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={formData.dateOfBirth}
                        onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender *</Label>
                    <Select value={formData.gender} onValueChange={(value) => handleInputChange("gender", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.genders?.map((gender: string) => (
                          <SelectItem key={gender} value={gender}>
                            {gender}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="bloodGroup">Blood Group</Label>
                    <Select value={formData.bloodGroup} onValueChange={(value) => handleInputChange("bloodGroup", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select blood group" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.bloodGroups?.map((bloodGroup: string) => (
                          <SelectItem key={bloodGroup} value={bloodGroup}>
                            {bloodGroup}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="rollNumber">Roll Number *</Label>
                    <Input
                      id="rollNumber"
                      value={formData.rollNumber}
                      onChange={(e) => handleInputChange("rollNumber", e.target.value)}
                      placeholder="Enter roll number"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Demographic Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Demographic Information</h4>
                <div className="grid gap-4 md:grid-cols-4">
                  <div>
                    <Label htmlFor="nationality">Nationality *</Label>
                    <Select value={formData.nationality} onValueChange={(value) => handleInputChange("nationality", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select nationality" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.nationalities?.map((nationality: string) => (
                          <SelectItem key={nationality} value={nationality}>
                            {nationality}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="religion">Religion</Label>
                    <Select value={formData.religion} onValueChange={(value) => handleInputChange("religion", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select religion" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.religions?.map((religion: string) => (
                          <SelectItem key={religion} value={religion}>
                            {religion}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.categories?.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="motherTongue">Mother Tongue</Label>
                    <Input
                      id="motherTongue"
                      value={formData.motherTongue}
                      onChange={(e) => handleInputChange("motherTongue", e.target.value)}
                      placeholder="Enter mother tongue"
                    />
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Address Information</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="address">Current Address *</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        placeholder="Enter current address"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="permanentAddress">Permanent Address</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="permanentAddress"
                        value={formData.permanentAddress}
                        onChange={(e) => handleInputChange("permanentAddress", e.target.value)}
                        placeholder="Enter permanent address"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-4">
                  <div>
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange("city", e.target.value)}
                      placeholder="Enter city"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State *</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => handleInputChange("state", e.target.value)}
                      placeholder="Enter state"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode *</Label>
                    <Input
                      id="pincode"
                      value={formData.pincode}
                      onChange={(e) => handleInputChange("pincode", e.target.value)}
                      placeholder="Enter pincode"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Input
                      id="country"
                      value={formData.country}
                      onChange={(e) => handleInputChange("country", e.target.value)}
                      placeholder="Enter country"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Medical & Emergency Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Medical & Emergency Information</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="emergencyContact">Emergency Contact *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="emergencyContact"
                        value={formData.emergencyContact}
                        onChange={(e) => handleInputChange("emergencyContact", e.target.value)}
                        placeholder="Enter emergency contact"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="medicalInfo">Medical Conditions</Label>
                    <Input
                      id="medicalInfo"
                      value={formData.medicalInfo}
                      onChange={(e) => handleInputChange("medicalInfo", e.target.value)}
                      placeholder="Any medical conditions, allergies, etc."
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="disabilities">Disabilities/Special Needs</Label>
                  <Input
                    id="disabilities"
                    value={formData.disabilities}
                    onChange={(e) => handleInputChange("disabilities", e.target.value)}
                    placeholder="Any disabilities or special needs"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Previous Education */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Previous Education & Documents
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="previousSchool">Previous School/Institution</Label>
                  <Input
                    id="previousSchool"
                    value={formData.previousSchool}
                    onChange={(e) => handleInputChange("previousSchool", e.target.value)}
                    placeholder="Enter previous school name"
                  />
                </div>
                <div>
                  <Label htmlFor="previousClass">Previous Class/Grade</Label>
                  <Select value={formData.previousClass} onValueChange={(value) => handleInputChange("previousClass", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select previous class" />
                    </SelectTrigger>
                    <SelectContent>
                      {admissionOptions?.previousClasses?.map((cls: string) => (
                        <SelectItem key={cls} value={cls}>
                          {cls}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div>
                  <Label htmlFor="tcNumber">Transfer Certificate Number</Label>
                  <Input
                    id="tcNumber"
                    value={formData.tcNumber}
                    onChange={(e) => handleInputChange("tcNumber", e.target.value)}
                    placeholder="Enter TC number"
                  />
                </div>
                <div>
                  <Label htmlFor="tcDate">TC Date</Label>
                  <Input
                    id="tcDate"
                    type="date"
                    value={formData.tcDate}
                    onChange={(e) => handleInputChange("tcDate", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="migrationCertificate">Migration Certificate</Label>
                  <Input
                    id="migrationCertificate"
                    value={formData.migrationCertificate}
                    onChange={(e) => handleInputChange("migrationCertificate", e.target.value)}
                    placeholder="Enter migration certificate number"
                  />
                </div>
              </div>

              {/* Document Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Document Numbers</h4>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="studentAadhar">Student Aadhar Number</Label>
                    <Input
                      id="studentAadhar"
                      value={formData.studentAadhar}
                      onChange={(e) => handleInputChange("studentAadhar", e.target.value)}
                      placeholder="Enter Aadhar number"
                      maxLength={12}
                    />
                  </div>
                  <div>
                    <Label htmlFor="birthCertificate">Birth Certificate Number</Label>
                    <Input
                      id="birthCertificate"
                      value={formData.birthCertificate}
                      onChange={(e) => handleInputChange("birthCertificate", e.target.value)}
                      placeholder="Enter birth certificate number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="casteCertificate">Caste Certificate Number</Label>
                    <Input
                      id="casteCertificate"
                      value={formData.casteCertificate}
                      onChange={(e) => handleInputChange("casteCertificate", e.target.value)}
                      placeholder="Enter caste certificate number"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Academic Information - Institute Specific */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Academic Information
                {admissionOptions && (
                  <Badge className="ml-2 bg-blue-100 text-blue-800">
                    {admissionOptions.institutionType === "school" ? "School" : "College"} Admission
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Hierarchical Program Selection */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">
                  {admissionOptions?.institutionType === "school" ? "Class Selection" : "Program Selection"}
                </h4>
                <HierarchicalProgramSelector
                  onSelectionChange={handleProgramSelection}
                  selectedProgramId={formData.programId}
                  disabled={isLoading}
                  className="w-full"
                />
              </div>

              {/* Batch Selection - Only show if program is selected */}
              {formData.programId && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Academic Session</h4>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="batch">
                        {admissionOptions?.institutionType === "school" ? "Academic Session" : "Academic Batch"} *
                      </Label>
                      <Select value={formData.batchId} onValueChange={(value) => handleInputChange("batchId", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder={
                            admissionOptions?.institutionType === "school" ? "Select session" : "Select batch"
                          } />
                        </SelectTrigger>
                        <SelectContent>
                          {availableBatches.map((batch: any) => (
                            <SelectItem key={batch.id} value={batch.id}>
                              {batch.batchName} ({batch.availableSeats} seats available)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Stream selection for grades 11-12 */}
                    {(formData.grade === "11" || formData.grade === "12") && (
                      <div>
                        <Label htmlFor="stream">Stream *</Label>
                        <Select value={formData.stream} onValueChange={(value) => handleInputChange("stream", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select stream" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Science">Science</SelectItem>
                            <SelectItem value="Commerce">Commerce</SelectItem>
                            <SelectItem value="Arts">Arts</SelectItem>
                            <SelectItem value="Vocational">Vocational</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Additional fields for school */}
                    {admissionOptions?.institutionType === "school" && (
                      <div>
                        <Label htmlFor="grade">Grade/Class *</Label>
                        <Select value={formData.grade} onValueChange={(value) => handleInputChange("grade", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select grade" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions.grades?.map((grade: string) => (
                              <SelectItem key={grade} value={grade}>
                                Grade {grade}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  {/* Stream selection for school grades 11-12 */}
                  {admissionOptions?.institutionType === "school" && (formData.grade === "11" || formData.grade === "12") && (
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="stream">Stream *</Label>
                        <Select value={formData.stream} onValueChange={(value) => handleInputChange("stream", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select stream" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions.streams?.map((stream: string) => (
                              <SelectItem key={stream} value={stream}>
                                {stream}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Section Assignment Display */}
              {sectionAssignment && formData.batchId && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-medium text-green-800 mb-2">Section Assignment</h4>
                  <div className="text-sm text-green-700">
                    <p>Assigned Section: <span className="font-medium">{sectionAssignment.assignedSection}</span></p>
                    <p>Available Seats: <span className="font-medium">{sectionAssignment.availableSeats}</span></p>
                    <p>Class Capacity: <span className="font-medium">{sectionAssignment.capacity}</span></p>
                  </div>
                </div>
              )}

              {/* Common Academic Fields */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Admission Details</h4>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="academicYear">Academic Year *</Label>
                    <Select value={formData.academicYear} onValueChange={(value) => handleInputChange("academicYear", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select academic year" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.academicYears?.map((year: string) => (
                          <SelectItem key={year} value={year}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="admissionType">Admission Type *</Label>
                    <Select value={formData.admissionType} onValueChange={(value) => handleInputChange("admissionType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select admission type" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.admissionTypes?.map((type: string) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="studentType">Student Category</Label>
                    <Select value={formData.studentType} onValueChange={(value) => handleInputChange("studentType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="regular">Regular</SelectItem>
                        <SelectItem value="lateral">Lateral Entry</SelectItem>
                        <SelectItem value="distance">Distance Learning</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="admissionDate">Admission Date *</Label>
                    <Input
                      id="admissionDate"
                      type="date"
                      value={formData.admissionDate}
                      onChange={(e) => handleInputChange("admissionDate", e.target.value)}
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Financial Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Financial Information</h4>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="feeCategory">Fee Category *</Label>
                    <Select value={formData.feeCategory} onValueChange={(value) => handleInputChange("feeCategory", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select fee category" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.feeCategories?.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="scholarshipType">Scholarship Type</Label>
                    <Select value={formData.scholarshipType} onValueChange={(value) => handleInputChange("scholarshipType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select scholarship type" />
                      </SelectTrigger>
                      <SelectContent>
                        {admissionOptions?.scholarshipTypes?.map((type: string) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="scholarshipAmount">Scholarship Amount</Label>
                    <Input
                      id="scholarshipAmount"
                      type="number"
                      value={formData.scholarshipAmount}
                      onChange={(e) => handleInputChange("scholarshipAmount", e.target.value)}
                      placeholder="Enter scholarship amount"
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="transportRequired"
                      checked={formData.transportRequired}
                      onCheckedChange={(checked) => handleInputChange("transportRequired", checked)}
                    />
                    <Label htmlFor="transportRequired">Transport Required</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hostelRequired"
                      checked={formData.hostelRequired}
                      onCheckedChange={(checked) => handleInputChange("hostelRequired", checked)}
                    />
                    <Label htmlFor="hostelRequired">Hostel Accommodation Required</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fee Structure Display */}
          {selectedFeeStructure && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <CreditCard className="h-5 w-5" />
                  Fee Structure - {admissionOptions?.currentAcademicYear}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Tuition Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.tuitionFee}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Admission Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.admissionFee}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exam Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.examFee}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Library Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.libraryFee}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Transport Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.transportFee}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Hostel Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.hostelFee}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Miscellaneous Fee:</span>
                      <span className="font-medium">₹{selectedFeeStructure.miscellaneousFee}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2 text-lg font-bold text-green-800">
                      <span>Total Fee:</span>
                      <span>₹{selectedFeeStructure.totalFee}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Parent/Guardian Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Parent/Guardian Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Parent Selection Option */}
              <div className="flex items-center space-x-2 mb-4">
                <Checkbox
                  id="useExistingParent"
                  checked={formData.useExistingParent}
                  onCheckedChange={(checked) => handleInputChange("useExistingParent", checked)}
                />
                <Label htmlFor="useExistingParent">Link to existing parent account</Label>
              </div>

              {formData.useExistingParent ? (
                /* Existing Parent Selection */
                <div>
                  <Label htmlFor="existingParentId">Select Parent *</Label>
                  <Select value={formData.existingParentId} onValueChange={(value) => handleInputChange("existingParentId", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Search and select parent" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableParents.map((parent: any) => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.firstName} {parent.lastName} - {parent.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500 mt-1">
                    Select an existing parent account to link this student
                  </p>
                </div>
              ) : (
                /* New Parent Creation */
                <div className="space-y-6">
                  {/* Father Information */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Father Information</h4>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="fatherFirstName">Father First Name *</Label>
                        <Input
                          id="fatherFirstName"
                          value={formData.fatherFirstName}
                          onChange={(e) => handleInputChange("fatherFirstName", e.target.value)}
                          placeholder="Enter father's first name"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="fatherLastName">Father Last Name *</Label>
                        <Input
                          id="fatherLastName"
                          value={formData.fatherLastName}
                          onChange={(e) => handleInputChange("fatherLastName", e.target.value)}
                          placeholder="Enter father's last name"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="fatherEmail">Father Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="fatherEmail"
                            type="email"
                            value={formData.fatherEmail}
                            onChange={(e) => handleInputChange("fatherEmail", e.target.value)}
                            placeholder="Enter father's email"
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="fatherPhone">Father Phone *</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="fatherPhone"
                            value={formData.fatherPhone}
                            onChange={(e) => handleInputChange("fatherPhone", e.target.value)}
                            placeholder="Enter father's phone"
                            className="pl-10"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-4">
                      <div>
                        <Label htmlFor="fatherOccupation">Father Occupation</Label>
                        <Select value={formData.fatherOccupation} onValueChange={(value) => handleInputChange("fatherOccupation", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select occupation" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions?.occupations?.map((occupation: string) => (
                              <SelectItem key={occupation} value={occupation}>
                                {occupation}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="fatherEducation">Father Education</Label>
                        <Select value={formData.fatherEducation} onValueChange={(value) => handleInputChange("fatherEducation", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select education" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions?.educationLevels?.map((level: string) => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="fatherIncome">Annual Income</Label>
                        <Input
                          id="fatherIncome"
                          type="number"
                          value={formData.fatherIncome}
                          onChange={(e) => handleInputChange("fatherIncome", e.target.value)}
                          placeholder="Enter annual income"
                        />
                      </div>
                      <div>
                        <Label htmlFor="fatherAadhar">Father Aadhar</Label>
                        <Input
                          id="fatherAadhar"
                          value={formData.fatherAadhar}
                          onChange={(e) => handleInputChange("fatherAadhar", e.target.value)}
                          placeholder="Enter Aadhar number"
                          maxLength={12}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Mother Information */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Mother Information</h4>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="motherFirstName">Mother First Name *</Label>
                        <Input
                          id="motherFirstName"
                          value={formData.motherFirstName}
                          onChange={(e) => handleInputChange("motherFirstName", e.target.value)}
                          placeholder="Enter mother's first name"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="motherLastName">Mother Last Name *</Label>
                        <Input
                          id="motherLastName"
                          value={formData.motherLastName}
                          onChange={(e) => handleInputChange("motherLastName", e.target.value)}
                          placeholder="Enter mother's last name"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="motherEmail">Mother Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="motherEmail"
                            type="email"
                            value={formData.motherEmail}
                            onChange={(e) => handleInputChange("motherEmail", e.target.value)}
                            placeholder="Enter mother's email"
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="motherPhone">Mother Phone</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="motherPhone"
                            value={formData.motherPhone}
                            onChange={(e) => handleInputChange("motherPhone", e.target.value)}
                            placeholder="Enter mother's phone"
                            className="pl-10"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-4">
                      <div>
                        <Label htmlFor="motherOccupation">Mother Occupation</Label>
                        <Select value={formData.motherOccupation} onValueChange={(value) => handleInputChange("motherOccupation", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select occupation" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions?.occupations?.map((occupation: string) => (
                              <SelectItem key={occupation} value={occupation}>
                                {occupation}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="motherEducation">Mother Education</Label>
                        <Select value={formData.motherEducation} onValueChange={(value) => handleInputChange("motherEducation", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select education" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionOptions?.educationLevels?.map((level: string) => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="motherIncome">Annual Income</Label>
                        <Input
                          id="motherIncome"
                          type="number"
                          value={formData.motherIncome}
                          onChange={(e) => handleInputChange("motherIncome", e.target.value)}
                          placeholder="Enter annual income"
                        />
                      </div>
                      <div>
                        <Label htmlFor="motherAadhar">Mother Aadhar</Label>
                        <Input
                          id="motherAadhar"
                          value={formData.motherAadhar}
                          onChange={(e) => handleInputChange("motherAadhar", e.target.value)}
                          placeholder="Enter Aadhar number"
                          maxLength={12}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Guardian Information (Optional) */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="guardianRequired"
                        checked={formData.guardianRequired}
                        onCheckedChange={(checked) => handleInputChange("guardianRequired", checked)}
                      />
                      <Label htmlFor="guardianRequired">Different Guardian Required</Label>
                    </div>

                    {formData.guardianRequired && (
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-900">Guardian Information</h4>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <Label htmlFor="guardianFirstName">Guardian First Name *</Label>
                            <Input
                              id="guardianFirstName"
                              value={formData.guardianFirstName}
                              onChange={(e) => handleInputChange("guardianFirstName", e.target.value)}
                              placeholder="Enter guardian's first name"
                              required={formData.guardianRequired}
                            />
                          </div>
                          <div>
                            <Label htmlFor="guardianLastName">Guardian Last Name *</Label>
                            <Input
                              id="guardianLastName"
                              value={formData.guardianLastName}
                              onChange={(e) => handleInputChange("guardianLastName", e.target.value)}
                              placeholder="Enter guardian's last name"
                              required={formData.guardianRequired}
                            />
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-3">
                          <div>
                            <Label htmlFor="guardianEmail">Guardian Email</Label>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="guardianEmail"
                                type="email"
                                value={formData.guardianEmail}
                                onChange={(e) => handleInputChange("guardianEmail", e.target.value)}
                                placeholder="Enter guardian's email"
                                className="pl-10"
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="guardianPhone">Guardian Phone *</Label>
                            <div className="relative">
                              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="guardianPhone"
                                value={formData.guardianPhone}
                                onChange={(e) => handleInputChange("guardianPhone", e.target.value)}
                                placeholder="Enter guardian's phone"
                                className="pl-10"
                                required={formData.guardianRequired}
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="guardianRelation">Relationship *</Label>
                            <Input
                              id="guardianRelation"
                              value={formData.guardianRelation}
                              onChange={(e) => handleInputChange("guardianRelation", e.target.value)}
                              placeholder="Enter relationship"
                              required={formData.guardianRequired}
                            />
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <Label htmlFor="guardianAddress">Guardian Address</Label>
                            <Input
                              id="guardianAddress"
                              value={formData.guardianAddress}
                              onChange={(e) => handleInputChange("guardianAddress", e.target.value)}
                              placeholder="Enter guardian's address"
                            />
                          </div>
                          <div>
                            <Label htmlFor="guardianAadhar">Guardian Aadhar</Label>
                            <Input
                              id="guardianAadhar"
                              value={formData.guardianAadhar}
                              onChange={(e) => handleInputChange("guardianAadhar", e.target.value)}
                              placeholder="Enter Aadhar number"
                              maxLength={12}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading} className="min-w-32">
              {isLoading ? "Creating..." : "Create Student"}
            </Button>
          </div>
        </form>

        {/* Important Notes */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <AlertCircle className="h-5 w-5" />
              Important Information & Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-blue-700 space-y-4">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Account Creation</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>A user account will be automatically created for the student</li>
                <li>Default password will be generated and should be shared securely</li>
                <li>Parent accounts will be created if they don&apos;t exist</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-blue-800 mb-2">Academic Information</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Roll number must be unique within the class/section</li>
                <li>Student will be auto-assigned to available section based on capacity</li>
                <li>Academic year selection affects fee structure and batch assignment</li>
                <li>Program selection determines available streams and branches</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-blue-800 mb-2">Required Documents</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Birth certificate and Aadhar card are mandatory for verification</li>
                <li>Transfer certificate required for students from other institutions</li>
                <li>Caste certificate required for reservation category students</li>
                <li>Income certificate required for scholarship applications</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-blue-800 mb-2">Financial Information</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Fee structure will be automatically assigned based on program and category</li>
                <li>Scholarship eligibility will be verified based on provided information</li>
                <li>Transport and hostel fees are additional to academic fees</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
