"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Edit, Trash2, <PERSON><PERSON><PERSON>, Search, Filter } from "lucide-react";
import { toast } from "sonner";

interface AcademicStream {
  id: string;
  name: string;
  code: string;
  type: string;
  institutionType: "school" | "college";
  description?: string;
  eligibilityCriteria?: string;
  duration: number;
  department?: string;
  isActive: boolean;
  displayOrder: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface InstitutionConfig {
  institutionType: "school" | "college";
  isConfigured: boolean;
}

export default function AcademicStreamsPage() {
  const [user, setUser] = useState<any>(null);
  const [streams, setStreams] = useState<AcademicStream[]>([]);
  const [institutionConfig, setInstitutionConfig] = useState<InstitutionConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingStream, setEditingStream] = useState<AcademicStream | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [isSaving, setIsSaving] = useState(false);
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    code: "",
    type: "science",
    description: "",
    eligibilityCriteria: "",
    duration: 1,
    department: "",
    displayOrder: 0,
  });

  // Stream type options based on institution type
  const getStreamTypeOptions = (institutionType: "school" | "college") => {
    if (institutionType === "school") {
      return [
        { value: "science", label: "Science" },
        { value: "commerce", label: "Commerce" },
        { value: "arts", label: "Arts" },
      ];
    } else {
      return [
        { value: "engineering", label: "Engineering" },
        { value: "medical", label: "Medical" },
        { value: "management", label: "Management" },
        { value: "science", label: "Science" },
        { value: "commerce", label: "Commerce" },
        { value: "arts", label: "Arts" },
        { value: "other", label: "Other" },
      ];
    }
  };

  const fetchInstitutionConfig = useCallback(async () => {
    try {
      const response = await fetch("/api/institution-config");
      const result = await response.json();

      if (response.ok && result.data) {
        setInstitutionConfig({
          institutionType: result.data.institutionType,
          isConfigured: result.isConfigured,
        });
        fetchStreams(result.data.institutionType);
      } else {
        toast.error("Institution must be configured first");
        router.push("/admin/institution-config");
      }
    } catch (error) {
      console.error("Error fetching institution config:", error);
      toast.error("Failed to fetch institution configuration");
    }
  }, [router]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin"].includes(parsedUser.role)) {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchInstitutionConfig();
  }, [router, fetchInstitutionConfig]);

  const fetchStreams = async (institutionType?: string) => {
    try {
      const params = new URLSearchParams();
      if (institutionType) {
        params.append("institutionType", institutionType);
      }

      const response = await fetch(`/api/academic-streams?${params}`);
      const result = await response.json();

      if (response.ok) {
        setStreams(result.data);
      } else {
        toast.error(result.error || "Failed to fetch streams");
      }
    } catch (error) {
      console.error("Error fetching streams:", error);
      toast.error("Failed to fetch academic streams");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!institutionConfig) return;

    setIsSaving(true);

    try {
      const url = editingStream ? `/api/academic-streams/${editingStream.id}` : "/api/academic-streams";
      const method = editingStream ? "PUT" : "POST";

      const payload = {
        ...formData,
        institutionType: institutionConfig.institutionType,
      };

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setIsDialogOpen(false);
        resetForm();
        fetchStreams(institutionConfig.institutionType);
      } else {
        toast.error(result.error || "Failed to save stream");
      }
    } catch (error) {
      console.error("Error saving stream:", error);
      toast.error("Failed to save academic stream");
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (stream: AcademicStream) => {
    setEditingStream(stream);
    setFormData({
      name: stream.name,
      code: stream.code,
      type: stream.type,
      description: stream.description || "",
      eligibilityCriteria: stream.eligibilityCriteria || "",
      duration: stream.duration,
      department: stream.department || "",
      displayOrder: stream.displayOrder,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (streamId: string) => {
    if (!confirm("Are you sure you want to deactivate this stream?")) return;

    try {
      const response = await fetch(`/api/academic-streams/${streamId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        fetchStreams(institutionConfig?.institutionType);
      } else {
        toast.error(result.error || "Failed to delete stream");
      }
    } catch (error) {
      console.error("Error deleting stream:", error);
      toast.error("Failed to delete academic stream");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      code: "",
      type: "science",
      description: "",
      eligibilityCriteria: "",
      duration: 1,
      department: "",
      displayOrder: 0,
    });
    setEditingStream(null);
  };

  const filteredStreams = streams.filter(stream => {
    const matchesSearch = stream.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         stream.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === "all" || stream.type === filterType;
    return matchesSearch && matchesFilter;
  });

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading streams...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!user || !institutionConfig) {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <BookOpen className="h-6 w-6" />
              Academic {institutionConfig.institutionType === "school" ? "Streams" : "Branches"}
            </h1>
            <p className="text-gray-600">
              Manage {institutionConfig.institutionType === "school" ? "academic streams" : "engineering branches"} for your {institutionConfig.institutionType}
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add {institutionConfig.institutionType === "school" ? "Stream" : "Branch"}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingStream ? "Edit" : "Add"} Academic {institutionConfig.institutionType === "school" ? "Stream" : "Branch"}
                </DialogTitle>
                <DialogDescription>
                  {editingStream ? "Update the" : "Create a new"} academic {institutionConfig.institutionType === "school" ? "stream" : "branch"} for your {institutionConfig.institutionType}.
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">{institutionConfig.institutionType === "school" ? "Stream" : "Branch"} Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder={`Enter ${institutionConfig.institutionType === "school" ? "stream" : "branch"} name`}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="code">Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                      placeholder="Enter code (e.g., SCI, CSE)"
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="type">Type *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getStreamTypeOptions(institutionConfig.institutionType).map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter description"
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? "Saving..." : editingStream ? "Update" : "Create"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search streams..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {getStreamTypeOptions(institutionConfig.institutionType).map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Streams Table */}
        <Card>
          <CardHeader>
            <CardTitle>
              {institutionConfig.institutionType === "school" ? "Academic Streams" : "Engineering Branches"} ({filteredStreams.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStreams.map((stream) => (
                    <TableRow key={stream.id}>
                      <TableCell className="font-medium">{stream.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{stream.code}</Badge>
                      </TableCell>
                      <TableCell className="capitalize">{stream.type}</TableCell>
                      <TableCell>{stream.duration} year{stream.duration > 1 ? "s" : ""}</TableCell>
                      <TableCell>
                        <Badge variant={stream.isActive ? "default" : "secondary"}>
                          {stream.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(stream)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(stream.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredStreams.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No {institutionConfig.institutionType === "school" ? "streams" : "branches"} found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
