import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { feeStructures, institutionConfig, academicPrograms } from "@/lib/db/schema";
import { eq, and, desc, ne } from "drizzle-orm";

const feeStructureSchema = z.object({
  grade: z.string().min(1, "Grade is required"),
  academicYear: z.string().min(1, "Academic year is required"),
  tuitionFee: z.string().min(1, "Tuition fee is required"),
  admissionFee: z.string().default("0"),
  examFee: z.string().default("0"),
  libraryFee: z.string().default("0"),
  transportFee: z.string().default("0"),
  hostelFee: z.string().default("0"),
  miscellaneousFee: z.string().default("0"),
  totalFee: z.string().min(1, "Total fee is required"),
  dueDate: z.string().optional(),
  status: z.enum(["active", "inactive"]).default("active"),
});

const app = new Hono()
  // Get all fee structures
  .get("/", async (c) => {
    try {
      const grade = c.req.query("grade");
      const academicYear = c.req.query("academicYear");
      const status = c.req.query("status");

      let whereConditions = [];

      if (grade) {
        whereConditions.push(eq(feeStructures.grade, grade));
      }

      if (academicYear) {
        whereConditions.push(eq(feeStructures.academicYear, academicYear));
      }

      if (status) {
        whereConditions.push(eq(feeStructures.status, status as any));
      }

      const whereClause = whereConditions.length > 0
        ? and(...whereConditions)
        : undefined;

      const fees = await db
        .select()
        .from(feeStructures)
        .where(whereClause)
        .orderBy(desc(feeStructures.createdAt));

      return c.json({ data: fees });
    } catch (error) {
      console.error("Error fetching fee structures:", error);
      return c.json({ error: "Failed to fetch fee structures" }, 500);
    }
  })

  // Get fee structure by grade and academic year
  .get("/by-grade/:grade/:academicYear", async (c) => {
    try {
      const grade = c.req.param("grade");
      const academicYear = c.req.param("academicYear");

      const [feeStructure] = await db
        .select()
        .from(feeStructures)
        .where(
          and(
            eq(feeStructures.grade, grade),
            eq(feeStructures.academicYear, academicYear),
            eq(feeStructures.status, "active")
          )
        );

      if (!feeStructure) {
        return c.json({ error: "Fee structure not found for this grade and academic year" }, 404);
      }

      return c.json({ data: feeStructure });
    } catch (error) {
      console.error("Error fetching fee structure:", error);
      return c.json({ error: "Failed to fetch fee structure" }, 500);
    }
  })

  // Create fee structure
  .post(
    "/",
    zValidator("json", feeStructureSchema),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Check if fee structure already exists for this grade and academic year
        const [existingFee] = await db
          .select()
          .from(feeStructures)
          .where(
            and(
              eq(feeStructures.grade, values.grade),
              eq(feeStructures.academicYear, values.academicYear)
            )
          );

        if (existingFee) {
          return c.json({
            error: "Fee structure already exists for this grade and academic year"
          }, 400);
        }

        const [newFeeStructure] = await db
          .insert(feeStructures)
          .values(values)
          .returning();

        return c.json({
          data: newFeeStructure,
          message: "Fee structure created successfully"
        }, 201);
      } catch (error) {
        console.error("Error creating fee structure:", error);
        return c.json({ error: "Failed to create fee structure" }, 500);
      }
    }
  )

  // Get fee structure by ID
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [feeStructure] = await db
        .select()
        .from(feeStructures)
        .where(eq(feeStructures.id, id));

      if (!feeStructure) {
        return c.json({ error: "Fee structure not found" }, 404);
      }

      return c.json({ data: feeStructure });
    } catch (error) {
      console.error("Error fetching fee structure:", error);
      return c.json({ error: "Failed to fetch fee structure" }, 500);
    }
  })

  // Update fee structure
  .put(
    "/:id",
    zValidator("json", feeStructureSchema.partial()),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        const [updatedFeeStructure] = await db
          .update(feeStructures)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(feeStructures.id, id))
          .returning();

        if (!updatedFeeStructure) {
          return c.json({ error: "Fee structure not found" }, 404);
        }

        return c.json({
          data: updatedFeeStructure,
          message: "Fee structure updated successfully"
        });
      } catch (error) {
        console.error("Error updating fee structure:", error);
        return c.json({ error: "Failed to update fee structure" }, 500);
      }
    }
  )

  // Delete fee structure
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Soft delete by setting status to inactive
      const [deletedFeeStructure] = await db
        .update(feeStructures)
        .set({
          status: "inactive",
          updatedAt: new Date(),
        })
        .where(eq(feeStructures.id, id))
        .returning();

      if (!deletedFeeStructure) {
        return c.json({ error: "Fee structure not found" }, 404);
      }

      return c.json({
        message: "Fee structure deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting fee structure:", error);
      return c.json({ error: "Failed to delete fee structure" }, 500);
    }
  })

  // Get institute-specific admission options
  .get("/admission-options", async (c) => {
    try {
      // Get institution configuration
      const [config] = await db
        .select()
        .from(institutionConfig)
        .orderBy(desc(institutionConfig.createdAt))
        .limit(1);

      if (!config) {
        return c.json({
          error: "Institution not configured. Please configure institution first."
        }, 400);
      }

      // Get active fee structures for current academic year
      const activeFees = await db
        .select()
        .from(feeStructures)
        .where(
          and(
            eq(feeStructures.academicYear, config.currentAcademicYear),
            eq(feeStructures.status, "active")
          )
        )
        .orderBy(feeStructures.grade);

      // Get active academic programs based on institution type
      const activePrograms = await db
        .select({
          id: academicPrograms.id,
          name: academicPrograms.name,
          code: academicPrograms.code,
          type: academicPrograms.type,
          duration: academicPrograms.duration,
          totalSemesters: academicPrograms.totalSemesters,
          department: academicPrograms.department,
          admissionStatus: academicPrograms.admissionStatus,
        })
        .from(academicPrograms)
        .where(
          and(
            eq(academicPrograms.status, "active"),
            eq(academicPrograms.admissionStatus, "open"),
            config.institutionType === "school"
              ? eq(academicPrograms.type, "school")
              : ne(academicPrograms.type, "school")
          )
        )
        .orderBy(academicPrograms.name);

      // Removed stream and branch fetching - school-only mode doesn't need these

      // Generate academic years (current + next 2 years)
      const currentYear = new Date().getFullYear();
      const academicYears = [];
      for (let i = 0; i < 3; i++) {
        const startYear = currentYear + i;
        const endYear = startYear + 1;
        academicYears.push(`${startYear}-${endYear.toString().slice(-2)}`);
      }

      // Common options for both school and college
      const commonOptions = {
        institutionType: config.institutionType,
        institutionName: config.institutionName,
        currentAcademicYear: config.currentAcademicYear,
        currency: config.currency,
        timezone: config.timezone,
        dateFormat: config.dateFormat,
        language: config.language,
        sessionStartMonth: config.sessionStartMonth,
        sessionEndMonth: config.sessionEndMonth,
        academicYears: academicYears,

        // Common demographic options
        genders: ["Male", "Female", "Other"],
        bloodGroups: ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"],
        categories: ["General", "OBC", "SC", "ST", "EWS"],
        religions: ["Hindu", "Muslim", "Christian", "Sikh", "Buddhist", "Jain", "Other"],
        nationalities: ["Indian", "Other"],

        // Financial categories
        feeCategories: ["Regular", "Scholarship", "Concession", "Management Quota"],
        scholarshipTypes: ["Merit", "Need-based", "Sports", "Cultural", "Minority", "Government"],

        // Educational qualifications
        educationLevels: ["Below 10th", "10th Pass", "12th Pass", "Graduate", "Post Graduate", "Doctorate"],

        // Occupations
        occupations: [
          "Government Service", "Private Service", "Business", "Agriculture",
          "Teaching", "Doctor", "Engineer", "Lawyer", "Self Employed",
          "Retired", "Housewife", "Other"
        ],

        feeStructures: activeFees,
      };

      // School-specific admission options
      const admissionOptions = {
        ...commonOptions,

        // School grades and structure
        grades: ["Nursery", "LKG", "UKG", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
        sections: ["A", "B", "C", "D", "E", "F"],
        // Removed streams array - handled at class level in school-only mode
        admissionTypes: ["New Admission", "Transfer Student", "Re-admission", "Readmission"],
        programs: activePrograms.filter(p => p.type === "school"), // Only school programs
        previousClasses: ["Nursery", "LKG", "UKG", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"],

        // School-specific options
        transportOptions: ["Not Required", "School Bus", "Private Transport"],
        hostelOptions: ["Day Scholar", "Hostel Resident"],
        feeCategories: ["Regular", "Scholarship", "Staff Ward", "Sibling Discount"],
        extracurricularActivities: ["Sports", "Music", "Dance", "Art", "Drama", "Debate", "Science Club"],
      };

      return c.json({ data: admissionOptions });
    } catch (error) {
      console.error("Error fetching admission options:", error);
      return c.json({ error: "Failed to fetch admission options" }, 500);
    }
  });

export default app;
