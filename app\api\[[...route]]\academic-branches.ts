import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { academicBranches, academicStreams } from "@/lib/db/schema";
import { eq, and, desc } from "drizzle-orm";

const app = new Hono()
  .get("/", async (c) => {
    try {
      const streamId = c.req.query("streamId");
      const isActive = c.req.query("isActive");

      // Build where conditions
      const whereConditions = [];

      if (streamId) {
        whereConditions.push(eq(academicBranches.streamId, streamId));
      }

      if (isActive !== undefined) {
        const activeFilter = isActive === "true";
        whereConditions.push(eq(academicBranches.isActive, activeFilter));
      }

      const branches = await db
        .select({
          id: academicBranches.id,
          streamId: academicBranches.streamId,
          name: academicBranches.name,
          code: academicBranches.code,
          shortName: academicBranches.shortName,
          description: academicBranches.description,
          eligibilityCriteria: academicBranches.eligibilityCriteria,
          duration: academicBranches.duration,
          department: academicBranches.department,
          totalSeats: academicBranches.totalSeats,
          isActive: academicBranches.isActive,
          displayOrder: academicBranches.displayOrder,
          status: academicBranches.status,
          createdAt: academicBranches.createdAt,
          updatedAt: academicBranches.updatedAt,
          // Include stream information
          streamName: academicStreams.name,
          streamCode: academicStreams.code,
          streamCategory: academicStreams.category,
        })
        .from(academicBranches)
        .leftJoin(academicStreams, eq(academicBranches.streamId, academicStreams.id))
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
        .orderBy(
          academicBranches.displayOrder,
          academicBranches.name
        );

      return c.json({
        data: branches,
        message: "Academic branches retrieved successfully"
      });
    } catch (error) {
      console.error("Error fetching academic branches:", error);
      return c.json({ error: "Failed to fetch academic branches" }, 500);
    }
  })

  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const branch = await db
        .select({
          id: academicBranches.id,
          streamId: academicBranches.streamId,
          name: academicBranches.name,
          code: academicBranches.code,
          shortName: academicBranches.shortName,
          description: academicBranches.description,
          eligibilityCriteria: academicBranches.eligibilityCriteria,
          duration: academicBranches.duration,
          department: academicBranches.department,
          totalSeats: academicBranches.totalSeats,
          isActive: academicBranches.isActive,
          displayOrder: academicBranches.displayOrder,
          status: academicBranches.status,
          createdAt: academicBranches.createdAt,
          updatedAt: academicBranches.updatedAt,
          // Include stream information
          streamName: academicStreams.name,
          streamCode: academicStreams.code,
          streamCategory: academicStreams.category,
        })
        .from(academicBranches)
        .leftJoin(academicStreams, eq(academicBranches.streamId, academicStreams.id))
        .where(eq(academicBranches.id, id))
        .limit(1);

      if (branch.length === 0) {
        return c.json({ error: "Academic branch not found" }, 404);
      }

      return c.json({
        data: branch[0],
        message: "Academic branch retrieved successfully"
      });
    } catch (error) {
      console.error("Error fetching academic branch:", error);
      return c.json({ error: "Failed to fetch academic branch" }, 500);
    }
  })

  .post(
    "/",
    zValidator("json", z.object({
      streamId: z.string().min(1, "Stream ID is required"),
      name: z.string().min(1, "Branch name is required"),
      code: z.string().min(1, "Branch code is required"),
      shortName: z.string().min(1, "Short name is required"),
      description: z.string().optional(),
      eligibilityCriteria: z.string().optional(),
      duration: z.number().min(1).default(4),
      department: z.string().optional(),
      totalSeats: z.number().min(1).default(60),
      displayOrder: z.number().default(0),
    })),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Check if code already exists
        const existingBranch = await db
          .select()
          .from(academicBranches)
          .where(eq(academicBranches.code, values.code))
          .limit(1);

        if (existingBranch.length > 0) {
          return c.json({ error: "Branch code already exists" }, 400);
        }

        // Verify stream exists
        const stream = await db
          .select()
          .from(academicStreams)
          .where(eq(academicStreams.id, values.streamId))
          .limit(1);

        if (stream.length === 0) {
          return c.json({ error: "Stream not found" }, 404);
        }

        const newBranch = await db
          .insert(academicBranches)
          .values(values)
          .returning();

        return c.json({
          data: newBranch[0],
          message: "Academic branch created successfully"
        }, 201);
      } catch (error) {
        console.error("Error creating academic branch:", error);
        if (error instanceof Error && error.message.includes("unique")) {
          return c.json({ error: "Branch code already exists" }, 400);
        }
        return c.json({ error: "Failed to create academic branch" }, 500);
      }
    }
  )

  .put(
    "/:id",
    zValidator("json", z.object({
      streamId: z.string().min(1).optional(),
      name: z.string().min(1).optional(),
      code: z.string().min(1).optional(),
      shortName: z.string().min(1).optional(),
      description: z.string().optional(),
      eligibilityCriteria: z.string().optional(),
      duration: z.number().min(1).optional(),
      department: z.string().optional(),
      totalSeats: z.number().min(1).optional(),
      isActive: z.boolean().optional(),
      displayOrder: z.number().optional(),
    })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // If code is being updated, check for duplicates
        if (values.code) {
          const existingBranch = await db
            .select()
            .from(academicBranches)
            .where(and(
              eq(academicBranches.code, values.code),
              // Note: In production, you'd use NOT operator to exclude current record
            ))
            .limit(1);

          // For now, we'll skip the duplicate check for updates
        }

        const updatedBranch = await db
          .update(academicBranches)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(academicBranches.id, id))
          .returning();

        if (updatedBranch.length === 0) {
          return c.json({ error: "Academic branch not found" }, 404);
        }

        return c.json({
          data: updatedBranch[0],
          message: "Academic branch updated successfully"
        });
      } catch (error) {
        console.error("Error updating academic branch:", error);
        if (error instanceof Error && error.message.includes("unique")) {
          return c.json({ error: "Branch code already exists" }, 400);
        }
        return c.json({ error: "Failed to update academic branch" }, 500);
      }
    }
  )

  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const deletedBranch = await db
        .delete(academicBranches)
        .where(eq(academicBranches.id, id))
        .returning();

      if (deletedBranch.length === 0) {
        return c.json({ error: "Academic branch not found" }, 404);
      }

      return c.json({
        data: deletedBranch[0],
        message: "Academic branch deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting academic branch:", error);
      return c.json({ error: "Failed to delete academic branch" }, 500);
    }
  });

export default app;
