"use client";

import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  Filter,
  GraduationCap,
  Eye,
  Edit,
  ArrowLeft
} from "lucide-react";

const statusColors = {
  open: "text-green-600 bg-green-100",
  closed: "text-red-600 bg-red-100",
  waitlist: "text-yellow-600 bg-yellow-100",
  active: "text-green-600 bg-green-100",
  inactive: "text-red-600 bg-red-100",
};

export default function AdminClassesPage() {
  const [programs, setPrograms] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentView, setCurrentView] = useState<"programs" | "batches" | "sections" | "students">("programs");
  const [selectedProgram, setSelectedProgram] = useState<any>(null);
  const [search, setSearch] = useState("");

  const fetchPrograms = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/academic-programs');
      const data = await response.json();
      setPrograms(data.data || []);
    } catch (error) {
      console.error('Error fetching programs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrograms();
  }, []);

  const handleProgramClick = (program: any) => {
    setSelectedProgram(program);
    setCurrentView("batches");
  };

  const handleBack = () => {
    if (currentView === "batches") {
      setCurrentView("programs");
      setSelectedProgram(null);
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              {currentView !== "programs" && (
                <Button variant="outline" size="sm" onClick={handleBack}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              )}
              <h1 className="text-2xl font-bold text-gray-900">Class Management</h1>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Program
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={`Search ${currentView}...`}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {currentView === "programs" && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {loading ? (
              <div className="col-span-full text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Loading programs...</p>
              </div>
            ) : programs.length === 0 ? (
              <div className="col-span-full text-center py-8 text-gray-500">
                <GraduationCap className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>No academic programs found</p>
              </div>
            ) : (
              programs.map((program) => (
                <Card key={program.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{program.name}</CardTitle>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[program.admissionStatus as keyof typeof statusColors]}`}>
                        {program.admissionStatus}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{program.code} • {program.department}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-blue-600">{program.statistics?.totalStudents || 0}</div>
                          <div className="text-gray-500">Total Students</div>
                        </div>
                        <div>
                          <div className="font-medium text-green-600">{program.statistics?.availableSeats || 0}</div>
                          <div className="text-gray-500">Available Seats</div>
                        </div>
                        <div>
                          <div className="font-medium text-purple-600">{program.statistics?.totalBatches || 0}</div>
                          <div className="text-gray-500">Batches</div>
                        </div>
                        <div>
                          <div className="font-medium text-orange-600">{program.statistics?.totalClasses || 0}</div>
                          <div className="text-gray-500">Classes</div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleProgramClick(program)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Batches
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {currentView === "batches" && selectedProgram && (
          <Card>
            <CardHeader>
              <CardTitle>Batches for {selectedProgram.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Batch view will be implemented here.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
