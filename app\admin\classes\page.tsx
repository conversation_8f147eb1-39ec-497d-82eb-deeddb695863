"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  GraduationCap,
  AlertCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

interface SchoolClass {
  id: string;
  name: string;
  code: string;
  totalSeats: number;
  admissionStatus: "open" | "closed" | "waitlist";
  status: "active" | "inactive";
  statistics?: {
    totalBatches: number;
    totalStudents: number;
    totalClasses: number;
    availableSeats: number;
  };
}

export default function ClassesPage() {
  const [user, setUser] = useState<any>(null);
  const [classes, setClasses] = useState<SchoolClass[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClass, setEditingClass] = useState<SchoolClass | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    totalSeats: 30,
    admissionStatus: "open" as const,
  });

  const router = useRouter();



  useEffect(() => {
    // Check authentication
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin"].includes(parsedUser.role)) {
      router.push("/");
      return;
    }

    setUser(parsedUser);
    loadClasses();
  }, [router]);

  const loadClasses = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/academic-programs");
      const result = await response.json();

      if (response.ok) {
        setClasses(result.data || []);
      } else {
        toast.error(result.error || "Failed to load classes");
      }
    } catch (error) {
      console.error("Error loading classes:", error);
      toast.error("Failed to load classes");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingClass
        ? `/api/academic-programs/${editingClass.id}`
        : "/api/academic-programs";

      const method = editingClass ? "PUT" : "POST";

      const payload = {
        ...formData,
        type: "school",
        duration: 1,
      };

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(editingClass ? "Class updated successfully" : "Class created successfully");
        setIsDialogOpen(false);
        resetForm();
        loadClasses();
      } else {
        toast.error(result.error || "Failed to save class");
      }
    } catch (error) {
      console.error("Error saving class:", error);
      toast.error("Failed to save class");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      code: "",
      totalSeats: 30,
      admissionStatus: "open",
    });
    setEditingClass(null);
  };

  const handleEdit = (classItem: SchoolClass) => {
    setFormData({
      name: classItem.name,
      code: classItem.code,
      totalSeats: classItem.totalSeats,
      admissionStatus: classItem.admissionStatus,
    });
    setEditingClass(classItem);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this class?")) return;

    try {
      const response = await fetch(`/api/academic-programs/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        toast.success("Class deleted successfully");
        loadClasses();
      } else {
        toast.error(result.error || "Failed to delete class");
      }
    } catch (error) {
      console.error("Error deleting class:", error);
      toast.error("Failed to delete class");
    }
  };

  // Filter classes based on search and status
  const filteredClasses = classes.filter(classItem => {
    const matchesSearch = classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         classItem.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === "all" || classItem.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <GraduationCap className="h-6 w-6" />
              Class Management
            </h1>
            <p className="text-gray-600">
              Manage school classes from Nursery to Grade 12
            </p>
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Class
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingClass ? "Edit Class" : "Add New Class"}
                </DialogTitle>
                <DialogDescription>
                  {editingClass ? "Update class information" : "Create a new class for your school"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="name">Class Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Grade 10"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="code">Class Code</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                      placeholder="e.g., GR10"
                      required
                    />
                  </div>



                  <div>
                    <Label htmlFor="totalSeats">Total Seats</Label>
                    <Input
                      id="totalSeats"
                      type="number"
                      value={formData.totalSeats}
                      onChange={(e) => setFormData(prev => ({ ...prev, totalSeats: parseInt(e.target.value) }))}
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="admissionStatus">Admission Status</Label>
                    <Select
                      value={formData.admissionStatus}
                      onValueChange={(value: "open" | "closed" | "waitlist") =>
                        setFormData(prev => ({ ...prev, admissionStatus: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                        <SelectItem value="waitlist">Waitlist</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingClass ? "Update Class" : "Create Class"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search classes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Classes Grid */}
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : filteredClasses.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No classes found</h3>
              <p className="text-gray-600 text-center mb-4">
                {searchTerm || filterStatus !== "all"
                  ? "No classes match your current filters."
                  : "Get started by creating your first class."
                }
              </p>
              {!searchTerm && filterStatus === "all" && (
                <Button onClick={() => setIsDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Class
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredClasses.map((classItem) => (
              <Card key={classItem.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{classItem.name}</CardTitle>
                      <CardDescription>
                        <span>{classItem.code}</span>
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={classItem.admissionStatus === "open" ? "default" : "secondary"}
                      >
                        {classItem.admissionStatus}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(classItem)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(classItem.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Total Seats:</span>
                      <span className="font-medium">{classItem.totalSeats}</span>
                    </div>

                    {classItem.statistics && (
                      <>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Students:</span>
                          <span className="font-medium">{classItem.statistics.totalStudents}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Available Seats:</span>
                          <span className="font-medium">{classItem.statistics.availableSeats}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Sessions:</span>
                          <span className="font-medium">{classItem.statistics.totalBatches}</span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </AppLayout>
  );
}
