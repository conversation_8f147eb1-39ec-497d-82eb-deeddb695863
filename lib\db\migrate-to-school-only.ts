import { config } from "dotenv";
import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import { readFileSync } from "fs";
import { join } from "path";

config({ path: ".env.local" });

async function migrateToSchoolOnly() {
  console.log("🔄 Starting migration to school-only mode...");

  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL is not set");
  }

  const sql = neon(process.env.DATABASE_URL);
  const db = drizzle(sql);

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), "lib/db/migrations/0004_school_only_migration.sql");
    const migrationSQL = readFileSync(migrationPath, "utf-8");

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith("--"));

    console.log(`📝 Executing ${statements.length} migration statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
          await sql(statement);
        } catch (error) {
          console.warn(`⚠️  Warning on statement ${i + 1}: ${error}`);
          // Continue with other statements even if one fails
        }
      }
    }

    console.log("✅ Migration to school-only mode completed successfully!");
    console.log("🏫 Your system is now configured for school management only.");
    console.log("📚 Standard school classes (Nursery to Grade 12) have been created.");
    
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  }
}

// Run the migration
migrateToSchoolOnly()
  .then(() => {
    console.log("🎉 Migration completed! You can now use the school-only system.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  });
