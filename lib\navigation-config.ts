import {
  LayoutDashboard,
  Users,
  GraduationCap,
  BookOpen,
  Calendar,
  ClipboardList,
  BarChart3,
  Settings,
  FileText,
  IndianRupee,
  Building,
  Bus,
  Award,
  Clock,
  MessageCircle,
  CreditCard,
  MapPin,
  Wrench,
  UserPlus,
  School,
  Library,
  Home,
  TrendingUp,
  BookMarked,
  Calculator,
  PieChart,
  Shield,
  RefreshCw,
  Bookmark,
  Grid3X3,
  Database,
} from "lucide-react";

export interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  description?: string;
  badge?: string;
  children?: NavigationItem[];
}

export interface RoleNavigation {
  role: string;
  dashboardPath: string;
  navigation: NavigationItem[];
}

// Define navigation items for each role
export const roleNavigationConfig: RoleNavigation[] = [
  // Super Admin - Full system access
  {
    role: "super_admin",
    dashboardPath: "/admin/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/admin/dashboard",
        icon: LayoutDashboard,
        description: "System overview and analytics"
      },
      {
        name: "User Management",
        href: "/admin/users",
        icon: Users,
        description: "Manage all system users",
        children: [
          { name: "All Users", href: "/admin/users", icon: Users },
          { name: "Add User", href: "/admin/users/new", icon: UserPlus },
          { name: "Roles & Permissions", href: "/admin/roles", icon: Shield }
        ]
      },
      {
        name: "School Setup",
        href: "/admin/institution-config",
        icon: Building,
        description: "Configure school information and settings",
        children: [
          { name: "School Configuration", href: "/admin/institution-config", icon: Settings },
        ]
      },
      {
        name: "Academic Management",
        href: "/admin/academic",
        icon: School,
        description: "Classes, sections and academic structure",
        children: [
          { name: "Classes", href: "/admin/programs", icon: GraduationCap },
          { name: "Academic Sessions", href: "/admin/batches", icon: Users },
          { name: "Sections", href: "/admin/sections", icon: Grid3X3 },
          { name: "Subjects", href: "/admin/subjects", icon: BookMarked }
        ]
      },
      {
        name: "Students",
        href: "/admin/students",
        icon: Users,
        description: "Student management"
      },
      {
        name: "Teachers",
        href: "/admin/teachers",
        icon: GraduationCap,
        description: "Faculty management"
      },
      {
        name: "Finance",
        href: "/admin/finance",
        icon: IndianRupee,
        description: "Financial management"
      },
      {
        name: "Reports & Analytics",
        href: "/admin/analytics",
        icon: BarChart3,
        description: "System reports and analytics",
        children: [
          { name: "Analytics Dashboard", href: "/admin/analytics", icon: BarChart3 },
          { name: "System Reports", href: "/admin/reports", icon: FileText },
          { name: "Audit Logs", href: "/admin/audit-logs", icon: Shield }
        ]
      },
      {
        name: "System Administration",
        href: "/admin/settings",
        icon: Settings,
        description: "System configuration and management",
        children: [
          { name: "System Settings", href: "/admin/settings", icon: Settings },
          { name: "System Backups", href: "/admin/system-backups", icon: Database },
          { name: "Maintenance", href: "/admin/maintenance", icon: Wrench }
        ]
      }
    ]
  },

  // Regular Admin - School administration
  {
    role: "admin",
    dashboardPath: "/admin/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/admin/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Students",
        href: "/admin/students",
        icon: Users,
        children: [
          { name: "All Students", href: "/admin/students", icon: Users },
          { name: "Add Student", href: "/admin/students/new", icon: UserPlus },
          { name: "Student Reports", href: "/admin/students/reports", icon: FileText }
        ]
      },
      {
        name: "Teachers",
        href: "/admin/teachers",
        icon: GraduationCap,
        children: [
          { name: "All Teachers", href: "/admin/teachers", icon: GraduationCap },
          { name: "Add Teacher", href: "/admin/teachers/new", icon: UserPlus },
          { name: "Teacher Reports", href: "/admin/teachers/reports", icon: FileText }
        ]
      },
      {
        name: "Classes",
        href: "/admin/classes",
        icon: BookOpen
      },
      {
        name: "Attendance",
        href: "/admin/attendance",
        icon: Calendar
      },
      {
        name: "Grades",
        href: "/admin/grades",
        icon: ClipboardList
      },
      {
        name: "Reports",
        href: "/admin/reports",
        icon: BarChart3
      },
      {
        name: "Settings",
        href: "/admin/settings",
        icon: Settings
      }
    ]
  },

  // Teacher - Class and student management
  {
    role: "teacher",
    dashboardPath: "/teacher/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/teacher/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "My Classes",
        href: "/teacher/classes",
        icon: BookOpen,
        description: "Classes you teach"
      },
      {
        name: "Students",
        href: "/teacher/students",
        icon: Users,
        description: "Your students"
      },
      {
        name: "Attendance",
        href: "/teacher/attendance",
        icon: Calendar,
        description: "Mark and view attendance"
      },
      {
        name: "Grades",
        href: "/teacher/grades",
        icon: ClipboardList,
        description: "Grade assignments and exams"
      },
      {
        name: "Assignments",
        href: "/teacher/assignments",
        icon: FileText,
        description: "Create and manage assignments"
      },
      {
        name: "Timetable",
        href: "/teacher/timetable",
        icon: Clock,
        description: "Your teaching schedule"
      },
      {
        name: "Reports",
        href: "/teacher/reports",
        icon: BarChart3,
        description: "Student progress reports"
      }
    ]
  },

  // Student - Academic progress and resources
  {
    role: "student",
    dashboardPath: "/student/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/student/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "My Grades",
        href: "/student/grades",
        icon: Award,
        description: "View your grades and progress"
      },
      {
        name: "Assignments",
        href: "/student/assignments",
        icon: FileText,
        description: "View and submit assignments"
      },
      {
        name: "Timetable",
        href: "/student/timetable",
        icon: Clock,
        description: "Your class schedule"
      },
      {
        name: "Attendance",
        href: "/student/attendance",
        icon: Calendar,
        description: "View your attendance record"
      },
      {
        name: "Library",
        href: "/student/library",
        icon: Library,
        description: "Library books and resources"
      },
      {
        name: "Exams",
        href: "/student/exams",
        icon: ClipboardList,
        description: "Exam schedules and results"
      },
      {
        name: "Fees",
        href: "/student/fees",
        icon: IndianRupee,
        description: "Fee status and payments"
      }
    ]
  },

  // Parent - Child's academic monitoring
  {
    role: "parent",
    dashboardPath: "/parent/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/parent/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Child's Progress",
        href: "/parent/progress",
        icon: TrendingUp,
        description: "Academic progress overview"
      },
      {
        name: "Grades & Reports",
        href: "/parent/grades",
        icon: Award,
        description: "View grades and report cards"
      },
      {
        name: "Attendance",
        href: "/parent/attendance",
        icon: Calendar,
        description: "Attendance records"
      },
      {
        name: "Timetable",
        href: "/parent/timetable",
        icon: Clock,
        description: "Class schedule"
      },
      {
        name: "Fee Status",
        href: "/parent/fees",
        icon: IndianRupee,
        description: "Fee payments and dues"
      },
      {
        name: "Communication",
        href: "/parent/messages",
        icon: MessageCircle,
        description: "Messages from teachers"
      },
      {
        name: "Events",
        href: "/parent/events",
        icon: Calendar,
        description: "School events and notices"
      }
    ]
  },

  // Admission Officer - Student admissions
  {
    role: "admission_officer",
    dashboardPath: "/admission/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/admission/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Applications",
        href: "/admission/applications",
        icon: FileText,
        description: "Manage admission applications"
      },
      {
        name: "Student Registration",
        href: "/admission/register",
        icon: UserPlus,
        description: "Register new students"
      },
      {
        name: "Programs",
        href: "/admission/programs",
        icon: GraduationCap,
        description: "Available programs"
      },
      {
        name: "Interviews",
        href: "/admission/interviews",
        icon: MessageCircle,
        description: "Schedule and manage interviews"
      },
      {
        name: "Reports",
        href: "/admission/reports",
        icon: BarChart3,
        description: "Admission statistics"
      }
    ]
  },

  // Finance Manager - Financial operations
  {
    role: "finance_manager",
    dashboardPath: "/finance/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/finance/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Fee Management",
        href: "/finance/fees",
        icon: IndianRupee,
        description: "Manage fee structures"
      },
      {
        name: "Payments",
        href: "/finance/payments",
        icon: CreditCard,
        description: "Process and track payments"
      },
      {
        name: "Invoices",
        href: "/finance/invoices",
        icon: FileText,
        description: "Generate and manage invoices"
      },
      {
        name: "Financial Reports",
        href: "/finance/reports",
        icon: PieChart,
        description: "Financial analytics and reports"
      },
      {
        name: "Expenses",
        href: "/finance/expenses",
        icon: Calculator,
        description: "Track school expenses"
      }
    ]
  },

  // Librarian - Library management
  {
    role: "librarian",
    dashboardPath: "/library/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/library/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Books",
        href: "/library/books",
        icon: BookOpen,
        description: "Manage book inventory"
      },
      {
        name: "Issue/Return",
        href: "/library/transactions",
        icon: RefreshCw,
        description: "Book issue and return"
      },
      {
        name: "Members",
        href: "/library/members",
        icon: Users,
        description: "Library members"
      },
      {
        name: "Reservations",
        href: "/library/reservations",
        icon: Bookmark,
        description: "Book reservations"
      },
      {
        name: "Reports",
        href: "/library/reports",
        icon: BarChart3,
        description: "Library usage reports"
      }
    ]
  },

  // Transport Manager - Transportation management
  {
    role: "transport_manager",
    dashboardPath: "/transport/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/transport/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Vehicles",
        href: "/transport/vehicles",
        icon: Bus,
        description: "Manage school vehicles"
      },
      {
        name: "Routes",
        href: "/transport/routes",
        icon: MapPin,
        description: "Manage transport routes"
      },
      {
        name: "Drivers",
        href: "/transport/drivers",
        icon: Users,
        description: "Manage drivers"
      },
      {
        name: "Maintenance",
        href: "/transport/maintenance",
        icon: Wrench,
        description: "Vehicle maintenance"
      },
      {
        name: "Students",
        href: "/transport/students",
        icon: Users,
        description: "Students using transport"
      },
      {
        name: "Reports",
        href: "/transport/reports",
        icon: BarChart3,
        description: "Transport reports"
      }
    ]
  },

  // Hostel Manager - Accommodation management
  {
    role: "hostel_manager",
    dashboardPath: "/hostel/dashboard",
    navigation: [
      {
        name: "Dashboard",
        href: "/hostel/dashboard",
        icon: LayoutDashboard
      },
      {
        name: "Rooms",
        href: "/hostel/rooms",
        icon: Building,
        description: "Manage hostel rooms"
      },
      {
        name: "Students",
        href: "/hostel/students",
        icon: Users,
        description: "Hostel residents"
      },
      {
        name: "Allocations",
        href: "/hostel/allocations",
        icon: Home,
        description: "Room allocations"
      },
      {
        name: "Maintenance",
        href: "/hostel/maintenance",
        icon: Wrench,
        description: "Facility maintenance"
      },
      {
        name: "Fees",
        href: "/hostel/fees",
        icon: IndianRupee,
        description: "Hostel fee management"
      },
      {
        name: "Reports",
        href: "/hostel/reports",
        icon: BarChart3,
        description: "Hostel reports"
      }
    ]
  }
];

// Helper function to get navigation for a specific role
export function getNavigationForRole(role: string): NavigationItem[] {
  const roleConfig = roleNavigationConfig.find(config => config.role === role);
  return roleConfig?.navigation || [];
}

// Helper function to get dashboard path for a specific role
export function getDashboardPathForRole(role: string): string {
  const roleConfig = roleNavigationConfig.find(config => config.role === role);
  return roleConfig?.dashboardPath || "/";
}

// Helper function to check if user has access to a specific route
export function hasAccessToRoute(userRole: string, route: string): boolean {
  const navigation = getNavigationForRole(userRole);

  const checkAccess = (items: NavigationItem[]): boolean => {
    return items.some(item => {
      if (item.href === route) return true;
      if (item.children) return checkAccess(item.children);
      return false;
    });
  };

  return checkAccess(navigation);
}
