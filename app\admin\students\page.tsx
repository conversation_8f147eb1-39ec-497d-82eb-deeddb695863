"use client";

import { useState, useEffect, use<PERSON><PERSON>back, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Users,
  Search,
  Plus,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  Calendar,
  RefreshCw,
  X,
  AlertCircle,
  Loader2,
  GraduationCap,
  BookOpen,
  Grid3X3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";
import { toast } from "sonner";

// Interfaces for hierarchical data
interface InstitutionConfig {
  institutionType: "school" | "college";
  isConfigured: boolean;
}

interface AcademicBatch {
  id: string;
  batchName: string;
  startYear: number;
  endYear: number;
  status: "active" | "completed" | "upcoming";
  programId: string;
  program?: {
    id: string;
    name: string;
    code: string;
  };
}

interface AcademicProgram {
  id: string;
  name: string;
  code: string;
  type: string;
}

interface AcademicStream {
  id: string;
  name: string;
  code: string;
  type: string;
}

interface AcademicSection {
  id: string;
  name: string;
  displayName: string;
  capacity: number;
  occupiedSeats: number;
  availableSeats: number;
}

interface Student {
  id: string;
  rollNumber: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  studentType: "regular" | "lateral" | "distance";
  admissionDate: string;
  status: "active" | "inactive" | "suspended";
  program?: {
    id: string;
    name: string;
    code: string;
  };
  batch?: {
    id: string;
    batchName: string;
  };
  section?: {
    id: string;
    name: string;
    displayName: string;
  };
  stream?: {
    id: string;
    name: string;
    code: string;
  };
}

const statusColors = {
  active: "text-green-600 bg-green-100",
  inactive: "text-red-600 bg-red-100",
  suspended: "text-yellow-600 bg-yellow-100",
};

const studentTypeColors = {
  regular: "text-blue-600 bg-blue-100",
  lateral: "text-purple-600 bg-purple-100",
  distance: "text-orange-600 bg-orange-100",
};

const batchStatusColors = {
  active: "text-green-600 bg-green-100",
  completed: "text-gray-600 bg-gray-100",
  upcoming: "text-blue-600 bg-blue-100",
};

function AdminStudentsContent() {
  const [user, setUser] = useState<any>(null);
  const [institutionConfig, setInstitutionConfig] = useState<InstitutionConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Hierarchical filter states
  const [selectedBatch, setSelectedBatch] = useState<string>(searchParams.get("batch") || "");
  const [selectedProgram, setSelectedProgram] = useState<string>(searchParams.get("program") || "");
  const [selectedStream, setSelectedStream] = useState<string>(searchParams.get("stream") || "all");
  const [selectedSection, setSelectedSection] = useState<string>(searchParams.get("section") || "all");
  const [searchTerm, setSearchTerm] = useState<string>(searchParams.get("search") || "");

  // Data states
  const [batches, setBatches] = useState<AcademicBatch[]>([]);
  const [programs, setPrograms] = useState<AcademicProgram[]>([]);
  const [streams, setStreams] = useState<AcademicStream[]>([]);
  const [sections, setSections] = useState<AcademicSection[]>([]);
  const [students, setStudents] = useState<Student[]>([]);

  // Loading states for dependent dropdowns
  const [loadingPrograms, setLoadingPrograms] = useState(false);
  const [loadingStreams, setLoadingStreams] = useState(false);
  const [loadingSections, setLoadingSections] = useState(false);
  const [loadingStudents, setLoadingStudents] = useState(false);

  const fetchInstitutionConfig = useCallback(async () => {
    try {
      const response = await fetch("/api/institution-config");
      const result = await response.json();

      if (response.ok && result.data) {
        setInstitutionConfig({
          institutionType: result.data.institutionType,
          isConfigured: result.isConfigured,
        });
      } else {
        toast.error("Institution must be configured first");
        router.push("/admin/institution-config");
      }
    } catch (error) {
      console.error("Error fetching institution config:", error);
      toast.error("Failed to fetch institution configuration");
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  // Removed stream fetching - school-only mode doesn't need streams

  const fetchSections = useCallback(async () => {
    if (!selectedProgram || !selectedBatch) return;

    setLoadingSections(true);
    try {
      const params = new URLSearchParams({
        programId: selectedProgram,
        batchId: selectedBatch,
        isActive: "true",
      });

      if (selectedStream !== "all") {
        params.append("streamId", selectedStream);
      }

      const response = await fetch(`/api/academic-sections?${params}`);
      const result = await response.json();

      if (response.ok) {
        setSections(result.data);
      } else {
        toast.error(result.error || "Failed to fetch sections");
      }
    } catch (error) {
      console.error("Error fetching sections:", error);
      toast.error("Failed to fetch academic sections");
    } finally {
      setLoadingSections(false);
    }
  }, [selectedProgram, selectedBatch, selectedStream]);

  const fetchStudents = useCallback(async () => {
    if (!selectedBatch || !selectedProgram) return;

    setLoadingStudents(true);
    try {
      const params = new URLSearchParams({
        batchId: selectedBatch,
        programId: selectedProgram,
      });

      if (selectedStream !== "all") {
        params.append("streamId", selectedStream);
      }

      if (selectedSection !== "all") {
        params.append("sectionId", selectedSection);
      }

      const response = await fetch(`/api/students?${params}`);
      const result = await response.json();

      if (response.ok) {
        setStudents(result.data);
      } else {
        toast.error(result.error || "Failed to fetch students");
      }
    } catch (error) {
      console.error("Error fetching students:", error);
      toast.error("Failed to fetch students");
    } finally {
      setLoadingStudents(false);
    }
  }, [selectedBatch, selectedProgram, selectedStream, selectedSection]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin"].includes(parsedUser.role)) {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchInstitutionConfig();
  }, [router, fetchInstitutionConfig]);

  useEffect(() => {
    if (institutionConfig?.isConfigured) {
      fetchBatches();
    }
  }, [institutionConfig]);

  useEffect(() => {
    if (selectedBatch) {
      fetchPrograms(selectedBatch);
      // Reset dependent filters
      setSelectedProgram("");
      setSelectedStream("all");
      setSelectedSection("all");
      setStudents([]);
    }
  }, [selectedBatch]);

  useEffect(() => {
    if (selectedProgram) {
      fetchStreams();
      fetchSections();
      // Reset dependent filters
      setSelectedStream("all");
      setSelectedSection("all");
    }
  }, [selectedProgram, selectedBatch, fetchStreams, fetchSections]);

  useEffect(() => {
    if (selectedBatch && selectedProgram) {
      fetchStudents();
    }
  }, [selectedBatch, selectedProgram, selectedStream, selectedSection, fetchStudents]);

  // Update URL parameters when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedBatch) params.set("batch", selectedBatch);
    if (selectedProgram) params.set("program", selectedProgram);
    if (selectedStream !== "all") params.set("stream", selectedStream);
    if (selectedSection !== "all") params.set("section", selectedSection);
    if (searchTerm) params.set("search", searchTerm);

    const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ""}`;
    window.history.replaceState({}, "", newUrl);
  }, [selectedBatch, selectedProgram, selectedStream, selectedSection, searchTerm]);

  const fetchBatches = async () => {
    try {
      const response = await fetch("/api/academic-batches");
      const result = await response.json();

      if (response.ok) {
        setBatches(result.data);
      } else {
        toast.error(result.error || "Failed to fetch batches");
      }
    } catch (error) {
      console.error("Error fetching batches:", error);
      toast.error("Failed to fetch academic batches");
    }
  };

  const fetchPrograms = async (batchId: string) => {
    if (!batchId) return;

    setLoadingPrograms(true);
    try {
      const response = await fetch(`/api/academic-programs?batchId=${batchId}`);
      const result = await response.json();

      if (response.ok) {
        setPrograms(result.data);
      } else {
        toast.error(result.error || "Failed to fetch programs");
      }
    } catch (error) {
      console.error("Error fetching programs:", error);
      toast.error("Failed to fetch academic programs");
    } finally {
      setLoadingPrograms(false);
    }
  };

  const clearFilters = () => {
    setSelectedBatch("");
    setSelectedProgram("");
    setSelectedStream("all");
    setSelectedSection("all");
    setSearchTerm("");
    setStudents([]);
  };

  const exportStudents = () => {
    if (filteredStudents.length === 0) {
      toast.error("No students to export");
      return;
    }

    const csvContent = [
      ["Roll Number", "Name", "Email", "Phone", "Student Type", "Program", "Batch", "Section", "Stream", "Status", "Admission Date"],
      ...filteredStudents.map(student => [
        student.rollNumber,
        `${student.firstName} ${student.lastName}`,
        student.email,
        student.phone,
        student.studentType,
        student.program?.name || "",
        student.batch?.batchName || "",
        student.section?.displayName || "",
        student.stream?.name || "",
        student.status,
        student.admissionDate,
      ])
    ].map(row => row.join(",")).join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `students-${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success("Students exported successfully");
  };

  // Filter students based on search term
  const filteredStudents = students.filter(student => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      student.rollNumber.toLowerCase().includes(searchLower) ||
      student.firstName.toLowerCase().includes(searchLower) ||
      student.lastName.toLowerCase().includes(searchLower) ||
      student.email.toLowerCase().includes(searchLower) ||
      student.phone.includes(searchTerm)
    );
  });

  // Calculate statistics
  const totalStudents = filteredStudents.length;
  const activeStudents = filteredStudents.filter(s => s.status === "active").length;
  const regularStudents = filteredStudents.filter(s => s.studentType === "regular").length;
  const lateralStudents = filteredStudents.filter(s => s.studentType === "lateral").length;

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            <p className="mt-2 text-gray-600">Loading student management...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!user || !institutionConfig) {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Users className="h-6 w-6" />
              Student Management
            </h1>
            <p className="text-gray-600">
              Hierarchical student filtering and management for {institutionConfig.institutionType}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearFilters}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
            <Button variant="outline" onClick={exportStudents} disabled={filteredStudents.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              Export ({filteredStudents.length})
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Student
            </Button>
          </div>
        </div>

        {/* Institution Type Badge */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {institutionConfig.institutionType === "school" ? "School Mode" : "College Mode"}
          </Badge>
          {!selectedBatch && (
            <Alert className="flex-1">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please select a session/batch to view students. Filters are hierarchical and mandatory.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Hierarchical Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Hierarchical Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Primary Filter: Session/Batch */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Session/Batch *
                </label>
                <Select value={selectedBatch} onValueChange={setSelectedBatch}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select session/batch" />
                  </SelectTrigger>
                  <SelectContent>
                    {batches.map((batch) => (
                      <SelectItem key={batch.id} value={batch.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{batch.batchName}</span>
                          <Badge
                            variant="outline"
                            className={`ml-2 ${batchStatusColors[batch.status]}`}
                          >
                            {batch.status}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Secondary Filter: Program/Class */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  {institutionConfig.institutionType === "school" ? "Class" : "Program"} *
                </label>
                <Select
                  value={selectedProgram}
                  onValueChange={setSelectedProgram}
                  disabled={!selectedBatch || loadingPrograms}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      !selectedBatch
                        ? "Select session first"
                        : loadingPrograms
                        ? "Loading..."
                        : `Select ${institutionConfig.institutionType === "school" ? "class" : "program"}`
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {programs.map((program) => (
                      <SelectItem key={program.id} value={program.id}>
                        {program.name} ({program.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Tertiary Filter: Stream/Branch */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  {institutionConfig.institutionType === "school" ? "Stream" : "Branch"}
                </label>
                <Select
                  value={selectedStream}
                  onValueChange={setSelectedStream}
                  disabled={!selectedProgram || loadingStreams}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      !selectedProgram
                        ? "Select program first"
                        : loadingStreams
                        ? "Loading..."
                        : `All ${institutionConfig.institutionType === "school" ? "streams" : "branches"}`
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All {institutionConfig.institutionType === "school" ? "Streams" : "Branches"}
                    </SelectItem>
                    {streams.map((stream) => (
                      <SelectItem key={stream.id} value={stream.id}>
                        {stream.name} ({stream.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Quaternary Filter: Section */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Section
                </label>
                <Select
                  value={selectedSection}
                  onValueChange={setSelectedSection}
                  disabled={!selectedProgram || loadingSections}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      !selectedProgram
                        ? "Select program first"
                        : loadingSections
                        ? "Loading..."
                        : "All sections"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sections</SelectItem>
                    {sections.map((section) => (
                      <SelectItem key={section.id} value={section.id}>
                        {section.displayName} ({section.occupiedSeats}/{section.capacity})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Search within filtered results */}
            {selectedBatch && selectedProgram && (
              <div className="border-t pt-4">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Search within filtered results
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by name, roll number, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                  {searchTerm && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                      onClick={() => setSearchTerm("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Student Stats */}
        {selectedBatch && selectedProgram && (
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-blue-600">
                      {totalStudents}
                    </div>
                    <p className="text-sm text-gray-600">Total Students</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <UserCheck className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-green-600">
                      {activeStudents}
                    </div>
                    <p className="text-sm text-gray-600">Active Students</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <GraduationCap className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-purple-600">
                      {regularStudents}
                    </div>
                    <p className="text-sm text-gray-600">Regular Students</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <BookOpen className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-orange-600">
                      {lateralStudents}
                    </div>
                    <p className="text-sm text-gray-600">Lateral Entry</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Students Table */}
        {selectedBatch && selectedProgram ? (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Students ({filteredStudents.length})
                  {loadingStudents && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
                {searchTerm && (
                  <Badge variant="outline">
                    Filtered by: &quot;{searchTerm}&quot;
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {loadingStudents ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p className="mt-2 text-gray-600">Loading students...</p>
                  </div>
                </div>
              ) : filteredStudents.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">No students found</p>
                  <p className="text-gray-400 text-sm">
                    {students.length === 0
                      ? "No students enrolled in the selected filters"
                      : "Try adjusting your search criteria"
                    }
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Roll Number</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Program</TableHead>
                        <TableHead>Section</TableHead>
                        <TableHead>Stream</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium text-gray-900">
                                {student.firstName} {student.lastName}
                              </div>
                              <div className="text-sm text-gray-500">{student.email}</div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{student.rollNumber}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={studentTypeColors[student.studentType]}
                            >
                              {student.studentType}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{student.program?.name}</div>
                              <div className="text-sm text-gray-500">{student.program?.code}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {student.section?.displayName || "-"}
                          </TableCell>
                          <TableCell>
                            {student.stream?.name || "-"}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {student.phone}
                              </div>
                              <div className="flex items-center text-gray-500">
                                <Mail className="h-3 w-3 mr-1" />
                                {student.email.split('@')[0]}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={statusColors[student.status]}
                            >
                              {student.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="sm">
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm" className="text-red-600">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-12">
              <div className="text-center">
                <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select Filters to View Students</h3>
                <p className="text-gray-500">
                  Please select at least a session/batch and {institutionConfig.institutionType === "school" ? "class" : "program"} to view students.
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  The filtering system follows a hierarchical structure for better organization.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}

export default function AdminStudentsPage() {
  return (
    <Suspense fallback={
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin mx-auto border-4 border-blue-600 border-t-transparent rounded-full" />
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    }>
      <AdminStudentsContent />
    </Suspense>
  );
}
