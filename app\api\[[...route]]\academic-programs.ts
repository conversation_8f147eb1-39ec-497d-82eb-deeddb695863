import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { academicPrograms, academicBatches, students, classes, institutionConfig } from "@/lib/db/schema";
import { eq, count, and, desc, sum } from "drizzle-orm";

// Validation schemas for school classes
const academicProgramSchema = z.object({
  name: z.string().min(2, "Class name must be at least 2 characters"),
  code: z.string().min(2, "Class code must be at least 2 characters"),
  type: z.enum(["school"]), // Only school type allowed
  classLevel: z.enum(["nursery", "lkg", "ukg", "grade_1", "grade_2", "grade_3", "grade_4", "grade_5", "grade_6", "grade_7", "grade_8", "grade_9", "grade_10", "grade_11", "grade_12"]),
  streamType: z.enum(["science", "commerce", "arts"]).optional(), // Only for Grade 11/12
  duration: z.number().min(1, "Duration must be at least 1 year").default(1),
  description: z.string().optional(),
  eligibilityCriteria: z.string().optional(),
  totalSeats: z.number().min(1, "Total seats must be at least 1"),
  admissionStatus: z.enum(["open", "closed", "waitlist"]).optional(),
});

const updateAcademicProgramSchema = z.object({
  name: z.string().min(2, "Class name must be at least 2 characters").optional(),
  code: z.string().min(2, "Class code must be at least 2 characters").optional(),
  type: z.enum(["school"]).optional(), // Only school type allowed
  classLevel: z.enum(["nursery", "lkg", "ukg", "grade_1", "grade_2", "grade_3", "grade_4", "grade_5", "grade_6", "grade_7", "grade_8", "grade_9", "grade_10", "grade_11", "grade_12"]).optional(),
  streamType: z.enum(["science", "commerce", "arts"]).optional(), // Only for Grade 11/12
  duration: z.number().min(1, "Duration must be at least 1 year").optional(),
  description: z.string().optional(),
  eligibilityCriteria: z.string().optional(),
  totalSeats: z.number().min(1, "Total seats must be at least 1").optional(),
  admissionStatus: z.enum(["open", "closed", "waitlist"]).optional(),
});

const app = new Hono()
  // Get all academic programs with statistics
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "10");
      const search = c.req.query("search");
      const type = c.req.query("type");
      const status = c.req.query("status");

      // Build query conditions
      let whereConditions = [];

      if (type) {
        whereConditions.push(eq(academicPrograms.type, type as any));
      }

      if (status) {
        whereConditions.push(eq(academicPrograms.status, status as any));
      }

      // Get programs with basic info
      const programs = await db
        .select({
          id: academicPrograms.id,
          name: academicPrograms.name,
          code: academicPrograms.code,
          type: academicPrograms.type,
          duration: academicPrograms.duration,
          totalSemesters: academicPrograms.totalSemesters,
          description: academicPrograms.description,
          eligibilityCriteria: academicPrograms.eligibilityCriteria,
          totalSeats: academicPrograms.totalSeats,
          department: academicPrograms.department,
          admissionStatus: academicPrograms.admissionStatus,
          status: academicPrograms.status,
          createdAt: academicPrograms.createdAt,
          updatedAt: academicPrograms.updatedAt,
        })
        .from(academicPrograms)
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

      // Filter by search if provided
      let filteredPrograms = programs;
      if (search) {
        const searchLower = search.toLowerCase();
        filteredPrograms = programs.filter(program =>
          program.name.toLowerCase().includes(searchLower) ||
          program.code.toLowerCase().includes(searchLower) ||
          (program.department && program.department.toLowerCase().includes(searchLower))
        );
      }

      // Get statistics for each program
      const programsWithStats = await Promise.all(
        filteredPrograms.map(async (program) => {
          // Get total batches
          const [batchCount] = await db
            .select({ count: count() })
            .from(academicBatches)
            .where(eq(academicBatches.programId, program.id));

          // Get total enrolled students
          const [studentCount] = await db
            .select({ count: count() })
            .from(students)
            .where(eq(students.programId, program.id));

          // Get total classes
          const [classCount] = await db
            .select({ count: count() })
            .from(classes)
            .where(eq(classes.programId, program.id));

          // Get current batch info
          const currentBatch = await db
            .select()
            .from(academicBatches)
            .where(
              and(
                eq(academicBatches.programId, program.id),
                eq(academicBatches.status, "active")
              )
            )
            .orderBy(academicBatches.startYear)
            .limit(1);

          return {
            ...program,
            statistics: {
              totalBatches: batchCount.count,
              totalStudents: studentCount.count,
              totalClasses: classCount.count,
              currentBatch: currentBatch[0] || null,
              availableSeats: currentBatch[0]?.availableSeats || 0,
            },
          };
        })
      );

      // Pagination
      const offset = (page - 1) * limit;
      const paginatedPrograms = programsWithStats.slice(offset, offset + limit);

      const meta = {
        page,
        limit,
        total: filteredPrograms.length,
        totalPages: Math.ceil(filteredPrograms.length / limit),
        hasNext: page < Math.ceil(filteredPrograms.length / limit),
        hasPrev: page > 1,
      };

      return c.json({ data: paginatedPrograms, meta });
    } catch (error) {
      console.error("Error fetching academic programs:", error);
      return c.json({ error: "Failed to fetch academic programs" }, 500);
    }
  })

  // Get single academic program with detailed statistics
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [program] = await db
        .select()
        .from(academicPrograms)
        .where(eq(academicPrograms.id, id));

      if (!program) {
        return c.json({ error: "Academic program not found" }, 404);
      }

      // Get detailed statistics
      const batches = await db
        .select()
        .from(academicBatches)
        .where(eq(academicBatches.programId, id));

      const [studentCount] = await db
        .select({ count: count() })
        .from(students)
        .where(eq(students.programId, id));

      const [classCount] = await db
        .select({ count: count() })
        .from(classes)
        .where(eq(classes.programId, id));

      return c.json({
        data: {
          ...program,
          batches,
          statistics: {
            totalBatches: batches.length,
            totalStudents: studentCount.count,
            totalClasses: classCount.count,
            totalAvailableSeats: batches.reduce((sum, batch) => sum + batch.availableSeats, 0),
          },
        },
      });
    } catch (error) {
      console.error("Error fetching academic program:", error);
      return c.json({ error: "Failed to fetch academic program" }, 500);
    }
  })

  // Create new academic program
  .post("/", zValidator("json", academicProgramSchema), async (c) => {
    try {
      const values = c.req.valid("json");

      // School-only validation
      if (values.type !== "school") {
        return c.json({ error: "Only school programs are supported" }, 400);
      }

      // School programs don't use semesters, just academic years
      const programData = {
        ...values,
      };

      const [newProgram] = await db
        .insert(academicPrograms)
        .values(programData)
        .returning();

      return c.json({ data: newProgram }, 201);
    } catch (error) {
      console.error("Error creating academic program:", error);
      if (error instanceof Error && error.message.includes("unique")) {
        return c.json({ error: "Program code already exists" }, 400);
      }
      return c.json({ error: "Failed to create academic program" }, 500);
    }
  })

  // Update academic program
  .put("/:id", zValidator("json", updateAcademicProgramSchema), async (c) => {
    try {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const [updatedProgram] = await db
        .update(academicPrograms)
        .set({ ...values, updatedAt: new Date() })
        .where(eq(academicPrograms.id, id))
        .returning();

      if (!updatedProgram) {
        return c.json({ error: "Academic program not found" }, 404);
      }

      return c.json({ data: updatedProgram });
    } catch (error) {
      console.error("Error updating academic program:", error);
      return c.json({ error: "Failed to update academic program" }, 500);
    }
  })

  // Delete academic program
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Check if program has students or batches
      const [studentCount] = await db
        .select({ count: count() })
        .from(students)
        .where(eq(students.programId, id));

      const [batchCount] = await db
        .select({ count: count() })
        .from(academicBatches)
        .where(eq(academicBatches.programId, id));

      if (studentCount.count > 0 || batchCount.count > 0) {
        return c.json({
          error: "Cannot delete program with existing students or batches"
        }, 400);
      }

      const [deletedProgram] = await db
        .delete(academicPrograms)
        .where(eq(academicPrograms.id, id))
        .returning();

      if (!deletedProgram) {
        return c.json({ error: "Academic program not found" }, 404);
      }

      return c.json({ message: "Academic program deleted successfully" });
    } catch (error) {
      console.error("Error deleting academic program:", error);
      return c.json({ error: "Failed to delete academic program" }, 500);
    }
  })

  // Get program batches
  .get("/:id/batches", async (c) => {
    try {
      const id = c.req.param("id");

      const batches = await db
        .select()
        .from(academicBatches)
        .where(eq(academicBatches.programId, id));

      // Get student count for each batch
      const batchesWithStats = await Promise.all(
        batches.map(async (batch) => {
          const [studentCount] = await db
            .select({ count: count() })
            .from(students)
            .where(eq(students.batchId, batch.id));

          return {
            ...batch,
            enrolledStudents: studentCount.count,
          };
        })
      );

      return c.json({ data: batchesWithStats });
    } catch (error) {
      console.error("Error fetching program batches:", error);
      return c.json({ error: "Failed to fetch program batches" }, 500);
    }
  })

  // Get hierarchical program data for selection components
  .get("/hierarchical", async (c) => {
    try {
      // Get institution configuration
      const [config] = await db
        .select()
        .from(institutionConfig)
        .orderBy(desc(institutionConfig.createdAt))
        .limit(1);

      if (!config) {
        return c.json({
          error: "Institution not configured. Please configure institution first."
        }, 400);
      }

      // Return school programs only (system is now school-focused)
      const programs = await db
        .select({
          id: academicPrograms.id,
          name: academicPrograms.name,
          code: academicPrograms.code,
          type: academicPrograms.type,
          duration: academicPrograms.duration,
          totalSemesters: academicPrograms.totalSemesters,
          department: academicPrograms.department,
          admissionStatus: academicPrograms.admissionStatus,
          totalSeats: academicPrograms.totalSeats,
        })
        .from(academicPrograms)
        .where(
          and(
            eq(academicPrograms.type, "school"),
            eq(academicPrograms.status, "active"),
            eq(academicPrograms.admissionStatus, "open")
          )
        )
        .orderBy(academicPrograms.name);

        // Calculate available seats for each program
        const programsWithSeats = await Promise.all(
          programs.map(async (program) => {
            const [batchStats] = await db
              .select({
                totalOccupied: sum(academicBatches.occupiedSeats),
                totalAvailable: sum(academicBatches.availableSeats),
              })
              .from(academicBatches)
              .where(eq(academicBatches.programId, program.id));

            return {
              ...program,
              availableSeats: batchStats?.totalAvailable || program.totalSeats,
            };
          })
        );

        return c.json({
          data: {
            programs: programsWithSeats,
            streams: [], // No streams needed for school system
          }
        });
    } catch (error) {
      console.error("Error fetching hierarchical program data:", error);
      return c.json({ error: "Failed to fetch hierarchical program data" }, 500);
    }
  });

export default app;
