"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, User, LogIn } from "lucide-react";

export default function TestLoginRedirectPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [hasUser, setHasUser] = useState(false);
  const [hasToken, setHasToken] = useState(false);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
    checkStorage();
  }, []);

  const checkStorage = () => {
    if (typeof window !== "undefined") {
      setHasUser(!!localStorage.getItem("user"));
      setHasToken(!!localStorage.getItem("token"));
    }
  };

  const testLogin = async (role: string, email: string, password: string) => {
    setIsLoading(true);

    try {
      console.log(`Testing login for ${role}...`);

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          role,
        }),
      });

      const data = await response.json();
      console.log("Login response:", data);

      if (!response.ok) {
        throw new Error(data.error || "Login failed");
      }

      // Store user data and token
      if (typeof window !== "undefined") {
        localStorage.setItem("user", JSON.stringify(data.data.user));
        localStorage.setItem("token", data.data.token);

        // Also set cookie for middleware
        document.cookie = `token=${data.data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;

        checkStorage();
      }

      console.log("User data stored:", data.data.user);
      toast.success(`Login successful for ${role}!`);

      // Redirect based on user role
      const redirectMap: Record<string, string> = {
        super_admin: "/admin/dashboard",
        admin: "/admin/dashboard",
        teacher: "/teacher/dashboard",
        student: "/student/dashboard",
        parent: "/parent/dashboard",
        admission_officer: "/admission/dashboard",
        finance_manager: "/finance/dashboard",
        librarian: "/library/dashboard",
        transport_manager: "/transport/dashboard",
        hostel_manager: "/hostel/dashboard",
      };

      const redirectPath = redirectMap[role] || "/";
      console.log("Redirecting to:", redirectPath);

      // Add a small delay to ensure localStorage is set
      setTimeout(() => {
        router.push(redirectPath);
      }, 500);

    } catch (error: any) {
      console.error("Login error:", error);
      toast.error(error.message || "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const testUsers = [
    {
      role: "student",
      email: "<EMAIL>",
      password: "student123",
      name: "Student (John Doe)",
      color: "bg-green-500",
    },
    {
      role: "teacher",
      email: "<EMAIL>",
      password: "teacher123",
      name: "Teacher (Emily Wilson)",
      color: "bg-blue-500",
    },
    {
      role: "admin",
      email: "<EMAIL>",
      password: "principal123",
      name: "Admin (Principal)",
      color: "bg-purple-500",
    },
    {
      role: "parent",
      email: "<EMAIL>",
      password: "parent123",
      name: "Parent (Jane Doe)",
      color: "bg-orange-500",
    },
  ];

  const clearStorage = () => {
    if (typeof window !== "undefined") {
      localStorage.clear();
      sessionStorage.clear();
      document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax";
      checkStorage();
      toast.success("Storage cleared!");
    }
  };

  if (!mounted) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Login Redirect Test Page
          </h1>
          <p className="text-gray-600">
            Test login functionality and redirection for different user roles
          </p>
        </div>

        {/* Current Storage Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Current Storage Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <span className="font-medium">User Data</span>
                <span className={`text-sm ${hasUser ? "text-green-600" : "text-red-600"}`}>
                  {hasUser ? "✓ Present" : "✗ Not found"}
                </span>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <span className="font-medium">Token</span>
                <span className={`text-sm ${hasToken ? "text-green-600" : "text-red-600"}`}>
                  {hasToken ? "✓ Present" : "✗ Not found"}
                </span>
              </div>
              <Button onClick={clearStorage} variant="outline" className="w-full">
                Clear Storage
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Login Buttons */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LogIn className="h-5 w-5 mr-2" />
              Test Login & Redirect
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {testUsers.map((user) => (
                <div key={user.role} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-10 h-10 ${user.color} rounded-full flex items-center justify-center`}>
                      <User className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </div>
                  <Button
                    onClick={() => testLogin(user.role, user.email, user.password)}
                    disabled={isLoading}
                    className="w-full"
                    variant="outline"
                  >
                    {isLoading ? "Logging in..." : `Login as ${user.role}`}
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Test Instructions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <p>1. Click on any user type to test login and redirection</p>
              <p>2. Check browser console for detailed logs</p>
              <p>3. Verify that you are redirected to the correct dashboard</p>
              <p>4. Use &quot;Clear Storage&quot; to reset between tests</p>
              <p>5. Pay special attention to student login redirection</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
