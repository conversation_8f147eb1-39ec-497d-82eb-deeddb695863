import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Types for branch management
export interface AcademicBranch {
  id: string;
  streamId: string;
  name: string;
  code: string;
  shortName: string;
  description?: string;
  eligibilityCriteria?: string;
  duration: number;
  department?: string;
  totalSeats: number;
  isActive: boolean;
  displayOrder: number;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  // Stream information
  streamName?: string;
  streamCode?: string;
  streamCategory?: string;
}

export interface CreateBranchData {
  streamId: string;
  name: string;
  code: string;
  shortName: string;
  description?: string;
  eligibilityCriteria?: string;
  duration?: number;
  department?: string;
  totalSeats?: number;
  displayOrder?: number;
}

export interface UpdateBranchData {
  streamId?: string;
  name?: string;
  code?: string;
  shortName?: string;
  description?: string;
  eligibilityCriteria?: string;
  duration?: number;
  department?: string;
  totalSeats?: number;
  isActive?: boolean;
  displayOrder?: number;
}

// Get all branches
export const useGetBranches = (params?: {
  streamId?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: ["branches", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      
      if (params?.streamId) {
        searchParams.append("streamId", params.streamId);
      }
      
      if (params?.isActive !== undefined) {
        searchParams.append("isActive", params.isActive.toString());
      }

      const response = await client.api["academic-branches"].$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch branches");
      }

      const data = await response.json();
      return data.data as AcademicBranch[];
    },
  });
};

// Get single branch
export const useGetBranch = (id: string) => {
  return useQuery({
    queryKey: ["branch", id],
    queryFn: async () => {
      const response = await client.api["academic-branches"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch branch");
      }

      const data = await response.json();
      return data.data as AcademicBranch;
    },
    enabled: !!id,
  });
};

// Create branch
export const useCreateBranch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBranchData) => {
      const response = await client.api["academic-branches"].$post({
        json: data,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create branch");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["branches"] });
      toast.success("Academic branch created successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Update branch
export const useUpdateBranch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateBranchData }) => {
      const response = await client.api["academic-branches"][":id"].$put({
        param: { id },
        json: data,
      });

      if (!response.ok) {
        const error = await response.json() as { error?: string };
        throw new Error(error.error || "Failed to update branch");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["branches"] });
      toast.success("Academic branch updated successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Delete branch
export const useDeleteBranch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["academic-branches"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const error = await response.json() as { error?: string };
        throw new Error(error.error || "Failed to delete branch");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["branches"] });
      toast.success("Academic branch deleted successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Get branches by stream (helper hook)
export const useGetBranchesByStream = (streamId?: string) => {
  return useGetBranches({
    streamId,
    isActive: true,
  });
};
