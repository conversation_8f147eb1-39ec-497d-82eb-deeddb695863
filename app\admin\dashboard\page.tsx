"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { StatsCards } from "@/components/dashboard/stats-cards";
import { RecentActivities } from "@/components/dashboard/recent-activities";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Users,
  GraduationCap,
  BookOpen,
  IndianRupee,
  Building,
  Bus,
  Calendar,
  FileText,
  Settings,
  Plus
} from "lucide-react";

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin"].includes(parsedUser.role)) {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const quickActions = [
    {
      title: "Add New Student",
      description: "Register a new student",
      icon: Users,
      color: "bg-blue-500",
      href: "/admin/students/new"
    },
    {
      title: "Add New Teacher",
      description: "Hire a new teacher",
      icon: GraduationCap,
      color: "bg-green-500",
      href: "/admin/teachers/new"
    },
    {
      title: "Create Class",
      description: "Set up a new class",
      icon: BookOpen,
      color: "bg-purple-500",
      href: "/admin/classes/new"
    },
    {
      title: "Fee Management",
      description: "Manage fee structures",
      icon: IndianRupee,
      color: "bg-yellow-500",
      href: "/admin/finance"
    },
    {
      title: "Hostel Management",
      description: "Manage hostel rooms",
      icon: Building,
      color: "bg-pink-500",
      href: "/admin/hostel"
    },
    {
      title: "Transport Routes",
      description: "Manage transport",
      icon: Bus,
      color: "bg-cyan-500",
      href: "/admin/transport"
    },
    {
      title: "Exam Schedule",
      description: "Schedule examinations",
      icon: Calendar,
      color: "bg-orange-500",
      href: "/admin/exams"
    },
    {
      title: "Generate Reports",
      description: "View system reports",
      icon: FileText,
      color: "bg-indigo-500",
      href: "/admin/reports"
    },
  ];

  const coreModules = [
    {
      title: "Student Management",
      description: "Manage student records, admissions, and profiles",
      icon: Users,
      count: "1,234",
      href: "/admin/students",
      color: "text-blue-600"
    },
    {
      title: "Teacher Management",
      description: "Manage teaching staff and faculty",
      icon: GraduationCap,
      count: "89",
      href: "/admin/teachers",
      color: "text-green-600"
    },
    {
      title: "Academic Management",
      description: "Classes, subjects, and curriculum",
      icon: BookOpen,
      count: "45",
      href: "/admin/academic",
      color: "text-purple-600"
    },
    {
      title: "Finance Management",
      description: "Fee collection, payments, and financial reports",
      icon: IndianRupee,
      count: "₹2.5M",
      href: "/admin/finance",
      color: "text-yellow-600"
    }
  ];

  const supportModules = [
    {
      title: "Library Management",
      description: "Books, issues, and library operations",
      icon: BookOpen,
      count: "5,678",
      href: "/admin/library",
      color: "text-indigo-600"
    },
    {
      title: "Transport Management",
      description: "Vehicles, routes, and transportation",
      icon: Bus,
      count: "12",
      href: "/admin/transport",
      color: "text-cyan-600"
    },
    {
      title: "Hostel Management",
      description: "Accommodation and hostel operations",
      icon: Building,
      count: "456",
      href: "/admin/hostel",
      color: "text-pink-600"
    }
  ];

  const adminModules = user?.role === "super_admin" ? [
    {
      title: "User Management",
      description: "Manage all system users and permissions",
      icon: Users,
      count: "1,456",
      href: "/admin/users",
      color: "text-red-600"
    },
    {
      title: "System Settings",
      description: "Configure system settings and preferences",
      icon: Settings,
      count: "",
      href: "/admin/settings",
      color: "text-gray-600"
    },
    {
      title: "Audit Logs",
      description: "Monitor system activity and user actions",
      icon: FileText,
      count: "2,345",
      href: "/admin/audit-logs",
      color: "text-orange-600"
    },
    {
      title: "System Backups",
      description: "Manage database backups and recovery",
      icon: Settings,
      count: "12",
      href: "/admin/system-backups",
      color: "text-teal-600"
    }
  ] : [];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              School Admin Dashboard
            </h1>
            <p className="text-gray-600">
              Welcome back, {user.firstName} {user.lastName}. Manage your school efficiently.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push("/admin/reports")}>
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button onClick={() => router.push("/admin/settings")}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <StatsCards />

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  className="p-4 text-left border rounded-lg hover:bg-gray-50 transition-colors"
                  onClick={() => router.push(action.href)}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {action.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Core Management Modules */}
        <Card>
          <CardHeader>
            <CardTitle>Core Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {coreModules.map((module, index) => (
                <div
                  key={index}
                  className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push(module.href)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <module.icon className={`h-8 w-8 ${module.color}`} />
                    {module.count && (
                      <span className="text-xl font-bold text-gray-900">
                        {module.count}
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {module.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {module.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Support Services */}
        <Card>
          <CardHeader>
            <CardTitle>Support Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {supportModules.map((module, index) => (
                <div
                  key={index}
                  className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push(module.href)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <module.icon className={`h-8 w-8 ${module.color}`} />
                    {module.count && (
                      <span className="text-xl font-bold text-gray-900">
                        {module.count}
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {module.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {module.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Super Admin Only Modules */}
        {user?.role === "super_admin" && adminModules.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                System Administration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {adminModules.map((module, index) => (
                  <div
                    key={index}
                    className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow bg-gradient-to-br from-gray-50 to-gray-100"
                    onClick={() => router.push(module.href)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <module.icon className={`h-8 w-8 ${module.color}`} />
                      {module.count && (
                        <span className="text-xl font-bold text-gray-900">
                          {module.count}
                        </span>
                      )}
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {module.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {module.description}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Activities */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <RecentActivities />
          </div>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Database</span>
                  <span className="text-green-600 text-sm font-medium">Online</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">API Services</span>
                  <span className="text-green-600 text-sm font-medium">Running</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Backup Status</span>
                  <span className="text-green-600 text-sm font-medium">Up to date</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Last Update</span>
                  <span className="text-gray-500 text-sm">2 hours ago</span>
                </div>
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full" onClick={() => router.push("/admin/audit-logs")}>
                    View System Logs
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
